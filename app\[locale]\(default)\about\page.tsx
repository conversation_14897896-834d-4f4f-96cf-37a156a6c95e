import Hero from "@/components/blocks/hero";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import { getAboutPage } from "@/services/page";

// 添加元数据配置
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/about`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/about`;
  }

  return {
    title: "About - Ghibli Style AI",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function AboutPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getAboutPage(locale);

  // 添加控制台日志以调试
  console.log("About page data:", page);

  return (
    <main className="flex min-h-screen flex-col">
      {page.hero && <Hero hero={page.hero} />}
      {page.story && <Feature1 section={page.story} />}
      {page.team && <Feature2 section={page.team} />}
    </main>
  );
}