'use client';

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { Quiz, QuizQuestion, QuizAttempt } from "@/types/quiz";
import { 
  Clock, 
  FileText, 
  Download, 
  Share2, 
  Edit3,
  CheckCircle,
  XCircle,
  AlertCircle,
  Check,
  Square,
  RotateCcw,
  Trophy,
  Target
} from "lucide-react";

interface QuizResultsEnhancedProps {
  quiz: Quiz;
  attempt?: QuizAttempt; // 新增：答题结果数据
  mode?: 'management' | 'results' | 'taking'; // 新增：显示模式
  onEdit?: (questionId: string) => void;
  onExport?: (format: string, selectedQuestions?: string[]) => void;
  onShare?: () => void;
  onRetake?: () => void; // 新增：重新答题回调
  onComplete?: (attempt: QuizAttempt) => void; // 新增：答题完成回调
}

export default function QuizResultsEnhanced({ 
  quiz, 
  attempt,
  mode = 'management',
  onEdit, 
  onExport, 
  onShare,
  onRetake,
  onComplete
}: QuizResultsEnhancedProps) {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set());
  const [selectedQuestions, setSelectedQuestions] = useState<Set<string>>(
    new Set(quiz.questions.map(q => q.id)) // 默认全选
  );
  const [isExporting, setIsExporting] = useState(false);
  
  // 答题模式的状态
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [timeSpent, setTimeSpent] = useState(0);
  const [startTime] = useState(Date.now());
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 计时器（仅在答题模式下启用）
  useEffect(() => {
    if (mode !== 'taking') return;
    
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [mode, startTime]);

  // 分析答题结果（仅在结果模式下使用）
  const analysisResults = useMemo(() => {
    if (!attempt) return null;

    const results = quiz.questions.map(question => {
      const userAnswer = attempt.answers[question.id];
      let isCorrect = false;
      let correctAnswer = '';
      let userAnswerText = '';

      switch (question.type) {
        case 'mcq':
          const correctOption = question.options?.find(opt => opt.isCorrect);
          correctAnswer = correctOption?.text || '';
          const userOption = question.options?.find(opt => opt.id === userAnswer);
          userAnswerText = userOption?.text || 'Not answered';
          isCorrect = correctOption?.id === userAnswer;
          break;
        
        case 'true_false':
          correctAnswer = question.correctAnswer === 'true' ? 'True' : 'False';
          userAnswerText = userAnswer === 'true' ? 'True' : userAnswer === 'false' ? 'False' : 'Not answered';
          isCorrect = userAnswer === question.correctAnswer;
          break;
        
        case 'short_answer':
          correctAnswer = question.correctAnswer || '';
          userAnswerText = (userAnswer as string) || 'Not answered';
          if (userAnswer && question.correctAnswer) {
            const similarity = calculateTextSimilarity(userAnswer as string, question.correctAnswer);
            isCorrect = similarity > 0.7;
          }
          break;
      }

      return {
        question,
        userAnswer: userAnswerText,
        correctAnswer,
        isCorrect,
        points: isCorrect ? (question.points || 1) : 0,
        maxPoints: question.points || 1
      };
    });

    const correctCount = results.filter(r => r.isCorrect).length;
    const incorrectCount = results.filter(r => !r.isCorrect).length;

    return {
      results,
      correctCount,
      incorrectCount,
      accuracy: Math.round((correctCount / quiz.questions.length) * 100)
    };
  }, [quiz, attempt]);

  // 简单的文本相似度计算
  const calculateTextSimilarity = (text1: string, text2: string): number => {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    const intersection = words1.filter(word => words2.includes(word));
    return intersection.length / Math.max(words1.length, words2.length);
  };

  // 处理答案选择（答题模式）
  const handleAnswerChange = (questionId: string, answer: string | string[]) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // 提交测验（答题模式）
  const handleSubmit = async () => {
    const unansweredQuestions = quiz.questions.filter(q => !answers[q.id]);
    
    if (unansweredQuestions.length > 0) {
      const confirmed = window.confirm(
        `You have ${unansweredQuestions.length} unanswered questions. Are you sure you want to submit?`
      );
      if (!confirmed) return;
    }

    setIsSubmitting(true);

    try {
      const { score, totalPoints } = calculateScore();
      const percentage = Math.round((score / totalPoints) * 100);

      const newAttempt: QuizAttempt = {
        id: `attempt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        quizId: quiz.id,
        answers,
        score,
        totalPoints,
        percentage,
        timeSpent,
        startedAt: new Date(startTime),
        completedAt: new Date(),
        isCompleted: true
      };

      await onComplete?.(newAttempt);
      toast.success('Quiz submitted successfully!');
    } catch (error) {
      toast.error('Submission failed, please try again later');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 计算分数
  const calculateScore = (): { score: number; totalPoints: number } => {
    let score = 0;
    let totalPoints = 0;

    quiz.questions.forEach(question => {
      const points = question.points || 1;
      totalPoints += points;

      const userAnswer = answers[question.id];
      if (!userAnswer) return;

      switch (question.type) {
        case 'mcq':
          const correctOption = question.options?.find(opt => opt.isCorrect);
          if (correctOption && userAnswer === correctOption.id) {
            score += points;
          }
          break;
        case 'true_false':
          if (userAnswer === question.correctAnswer) {
            score += points;
          }
          break;
        case 'short_answer':
          if (userAnswer && question.correctAnswer) {
            const similarity = calculateTextSimilarity(
              userAnswer as string, 
              question.correctAnswer
            );
            if (similarity > 0.7) {
              score += points;
            }
          }
          break;
      }
    });

    return { score, totalPoints };
  };

  // 计算选中题目的统计信息
  const selectedStats = useMemo(() => {
    const selected = quiz.questions.filter(q => selectedQuestions.has(q.id));
    return {
      count: selected.length,
      totalPoints: selected.reduce((sum, q) => sum + (q.points || 1), 0),
      estimatedTime: Math.ceil(selected.length * 2) // 每题约2分钟
    };
  }, [selectedQuestions, quiz.questions]);

  const toggleQuestion = (questionId: string) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionId)) {
      newExpanded.delete(questionId);
    } else {
      newExpanded.add(questionId);
    }
    setExpandedQuestions(newExpanded);
  };

  const toggleQuestionSelection = (questionId: string) => {
    const newSelected = new Set(selectedQuestions);
    if (newSelected.has(questionId)) {
      newSelected.delete(questionId);
    } else {
      newSelected.add(questionId);
    }
    setSelectedQuestions(newSelected);
  };

  const selectAllQuestions = () => {
    setSelectedQuestions(new Set(quiz.questions.map(q => q.id)));
    toast.success('All questions selected');
  };

  const deselectAllQuestions = () => {
    setSelectedQuestions(new Set());
    toast.success('All questions deselected');
  };

  const handleExport = async (format: string) => {
    if (selectedQuestions.size === 0) {
      toast.error('Please select at least one question to export');
      return;
    }

    setIsExporting(true);
    try {
      await onExport?.(format, Array.from(selectedQuestions));
      toast.success(`Exporting ${selectedQuestions.size} questions as ${format.toUpperCase()} format`);
    } catch (error) {
      toast.error('Export failed, please try again later');
    } finally {
      setIsExporting(false);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    // 完全移除题目类型图标，避免用户混淆
    return null;
  };

  const getTypeName = (type: string) => {
    switch (type) {
      case 'mcq': return 'Multiple Choice';
      case 'true_false': return 'True/False';
      case 'short_answer': return 'Short Answer';
      case 'essay': return 'Essay';
      default: return 'Unknown Type';
    }
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    if (percentage >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBadge = (percentage: number) => {
    if (percentage >= 90) return { text: 'Excellent', color: 'bg-green-100 text-green-800' };
    if (percentage >= 80) return { text: 'Good', color: 'bg-blue-100 text-blue-800' };
    if (percentage >= 70) return { text: 'Average', color: 'bg-yellow-100 text-yellow-800' };
    if (percentage >= 60) return { text: 'Pass', color: 'bg-orange-100 text-orange-800' };
    return { text: 'Fail', color: 'bg-red-100 text-red-800' };
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const isAllSelected = selectedQuestions.size === quiz.questions.length;
  const isPartialSelected = selectedQuestions.size > 0 && selectedQuestions.size < quiz.questions.length;
  const answeredCount = Object.keys(answers).length;

  // 添加 ref 来处理 indeterminate 状态
  const allSelectCheckboxRef = useRef<HTMLInputElement>(null);

  // 在 useEffect 中设置 indeterminate 状态
  useEffect(() => {
    if (allSelectCheckboxRef.current) {
      allSelectCheckboxRef.current.indeterminate = isPartialSelected;
    }
  }, [isPartialSelected]);

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* 测验概览 */}
      <Card className={mode === 'results' ? 'border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50' : ''}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {mode === 'results' && <Trophy className="w-8 h-8 text-yellow-500" />}
              <div>
                <CardTitle className="text-2xl">
                  {mode === 'results' ? 'Quiz Completed!' : quiz.title}
                </CardTitle>
                {mode === 'results' ? (
                  <p className="text-muted-foreground">{quiz.title}</p>
                ) : quiz.description && (
                  <p className="text-muted-foreground">{quiz.description}</p>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {mode === 'results' && attempt && (
                <Badge className={getScoreBadge(attempt.percentage).color}>
                  {getScoreBadge(attempt.percentage).text}
                </Badge>
              )}
              {mode === 'taking' && (
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">Time</p>
                  <p className="font-mono text-lg font-semibold">{formatTime(timeSpent)}</p>
                </div>
              )}
              <Badge variant="outline" className={getDifficultyColor(quiz.difficulty || 'medium')}>
                {quiz.difficulty === 'easy' ? 'Easy' : 
                 quiz.difficulty === 'medium' ? 'Medium' : 
                 quiz.difficulty === 'hard' ? 'Hard' : 'Unknown'}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 成绩统计（结果模式） */}
          {mode === 'results' && attempt && analysisResults && (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-6 mb-6">
              <div className="text-center">
                <div className={`text-4xl font-bold ${getScoreColor(attempt.percentage)}`}>
                  {attempt.percentage}%
                </div>
                <p className="text-sm text-muted-foreground mt-1">Total Score</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {attempt.score}
                </div>
                <p className="text-sm text-muted-foreground mt-1">Score</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-600">
                  {attempt.totalPoints}
                </div>
                <p className="text-sm text-muted-foreground mt-1">Total Points</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {analysisResults.correctCount}
                </div>
                <p className="text-sm text-muted-foreground mt-1">Correct Answers</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">
                  {Math.floor(attempt.timeSpent / 60)}:{(attempt.timeSpent % 60).toString().padStart(2, '0')}
                </div>
                <p className="text-sm text-muted-foreground mt-1">Time Spent</p>
              </div>
            </div>
          )}

          {/* 基础统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">Questions</p>
                <p className="font-semibold">{quiz.totalQuestions}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Points</p>
                <p className="font-semibold">{quiz.totalPoints}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">
                  {mode === 'taking' ? 'Time Spent' : 'Estimated Time'}
                </p>
                <p className="font-semibold">
                  {mode === 'taking' ? formatTime(timeSpent) : `${quiz.estimatedTime || 10} minutes`}
                </p>
              </div>
            </div>
            {mode === 'taking' && (
              <div className="flex items-center space-x-2">
                <Target className="w-5 h-5 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Answered</p>
                  <p className="font-semibold">{answeredCount}/{quiz.questions.length}</p>
                </div>
              </div>
            )}
            {mode !== 'taking' && (
              <div className="flex items-center space-x-2">
                <FileText className="w-5 h-5 text-purple-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Source File</p>
                  <p className="font-semibold text-xs truncate">
                    {quiz.sourceFile?.name || 'Unknown'}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* 进度条（答题模式） */}
          {mode === 'taking' && (
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>Progress</span>
                <span>{answeredCount} / {quiz.questions.length}</span>
              </div>
              <Progress value={(answeredCount / quiz.questions.length) * 100} className="h-2" />
            </div>
          )}

          {/* 选中题目统计（管理模式） */}
          {mode === 'management' && selectedQuestions.size > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-sm font-medium text-blue-800">
                    Selected {selectedStats.count} questions
                  </span>
                  <span className="text-sm text-blue-600">
                    Total points: {selectedStats.totalPoints}
                  </span>
                  <span className="text-sm text-blue-600">
                    Estimated time: {selectedStats.estimatedTime} minutes
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex flex-wrap gap-3">
            {mode === 'results' && onRetake && (
              <Button onClick={onRetake} className="bg-blue-600 hover:bg-blue-700">
                <RotateCcw className="w-4 h-4 mr-2" />
                Retake Quiz
              </Button>
            )}
            {mode === 'taking' && (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
              </Button>
            )}
            
            {/* {mode === 'management' && (
              <>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleExport('pdf')}
                  disabled={isExporting || selectedQuestions.size === 0}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isExporting ? 'Exporting...' : 'Export PDF'}
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleExport('docx')}
                  disabled={isExporting || selectedQuestions.size === 0}
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isExporting ? 'Exporting...' : 'Export Word'}
                </Button>
              </>
            )} */}
             
             {mode === 'management' && (
  <>
    <Button 
      variant="outline" 
      size="sm"
      disabled={true}
      className="opacity-50 cursor-not-allowed"
    >
      <Download className="w-4 h-4 mr-2" />
      Export PDF (Coming Soon)
    </Button>
    <Button 
      variant="outline" 
      size="sm"
      onClick={() => handleExport('docx')}
      disabled={isExporting || selectedQuestions.size === 0}
    >
      <Download className="w-4 h-4 mr-2" />
      {isExporting ? 'Exporting...' : 'Export Word'}
    </Button>
  </>
)}

            {(mode === 'management' || mode === 'results') && onShare && (
              <Button variant="outline" size="sm" onClick={onShare}>
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 题目选择控制（仅管理模式） */}
      {mode === 'management' && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    ref={allSelectCheckboxRef}
                    checked={isAllSelected}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        selectAllQuestions();
                      } else {
                        deselectAllQuestions();
                      }
                    }}
                  />
                  <span className="text-sm font-medium">
                    {isAllSelected ? 'All selected' : isPartialSelected ? 'Partially selected' : 'None selected'}
                  </span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {selectedQuestions.size} / {quiz.questions.length} questions selected
                </span>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllQuestions}
                  disabled={isAllSelected}
                >
                  <Check className="w-4 h-4 mr-2" />
                  Select All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={deselectAllQuestions}
                  disabled={selectedQuestions.size === 0}
                >
                  <Square className="w-4 h-4 mr-2" />
                  Deselect All
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 题目列表 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          {mode === 'results' ? 'Answer Analysis' : 'Quiz Questions'}
        </h3>
        {quiz.questions.map((question, index) => {
          const isSelected = selectedQuestions.has(question.id);
          const userAnswer = answers[question.id];
          const hasAnswered = !!userAnswer;
          
          // 结果模式的分析数据
          const resultData = mode === 'results' && analysisResults 
            ? analysisResults.results.find(r => r.question.id === question.id)
            : null;

          return (
            <Card 
              key={question.id} 
              className={`overflow-hidden transition-all ${
                mode === 'management' && isSelected ? 'ring-2 ring-blue-200 bg-blue-50/30' : 
                mode === 'results' && resultData ? 
                  (resultData.isCorrect ? 'border-green-200 bg-green-50/30' : 'border-red-200 bg-red-50/30') :
                mode === 'taking' && hasAnswered ? 'border-green-200 bg-green-50/30' : ''
              }`}
            >
              <CardHeader 
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => toggleQuestion(question.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {/* 根据模式显示不同的左侧元素 */}
                    {mode === 'management' && (
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleQuestionSelection(question.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    )}
                    
                 
                   {mode === 'results' && resultData && (
                      resultData.isCorrect ? (
                        <CheckCircle className="w-6 h-6 text-green-600" />
                      ) : (
                        <XCircle className="w-6 h-6 text-red-600" />
                      )
                    )}
                    
                    <span className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold ${
                      mode === 'management' && isSelected ? 'bg-blue-100 text-blue-800' :
                      mode === 'results' && resultData ? 
                        (resultData.isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') :
                      mode === 'taking' && hasAnswered ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {index + 1}
                    </span>
                    
                    <div className="flex items-center space-x-2">
                      {/* {getTypeIcon(question.type)} */}
                      <Badge variant="secondary" className="text-xs">
                        {getTypeName(question.type)}
                      </Badge>
                      <Badge variant="outline" className={`text-xs ${getDifficultyColor(question.difficulty || 'medium')}`}>
                        {question.difficulty === 'easy' ? 'Easy' : 
                         question.difficulty === 'medium' ? 'Medium' : 
                         question.difficulty === 'hard' ? 'Hard' : 'Unknown'}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {mode === 'results' && resultData ? 
                          `${resultData.points}/${resultData.maxPoints} points` :
                          `${question.points || 1} points`
                        }
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {mode === 'results' && resultData && (
                      <Badge variant={resultData.isCorrect ? 'default' : 'destructive'}>
                        {resultData.isCorrect ? 'Correct' : 'Incorrect'}
                      </Badge>
                    )}
                    {mode === 'taking' && hasAnswered && (
                      <Badge variant="secondary">Answered</Badge>
                    )}
                    {mode === 'management' && onEdit && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(question.id);
                        }}
                      >
                        <Edit3 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
                <p className="text-left font-medium mt-2">{question.text}</p>
              </CardHeader>
              
              {expandedQuestions.has(question.id) && (
                <CardContent>
                  <Separator className="mb-4" />
                  
                  {/* 答题模式：显示交互式选项 */}
                  {mode === 'taking' && (
                    <>
                      {/* 选择题选项 */}
                      {question.type === 'mcq' && question.options && (
                        <RadioGroup
                          value={userAnswer as string || ''}
                          onValueChange={(value) => handleAnswerChange(question.id, value)}
                          className="space-y-3"
                        >
                          {question.options.map((option) => (
                            <div key={option.id} className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-gray-50 transition-colors cursor-pointer">
                              <RadioGroupItem value={option.id} id={`${question.id}-${option.id}`} />
                              <Label htmlFor={`${question.id}-${option.id}`} className="flex-1 cursor-pointer">
                                <span className="font-medium mr-2">{option.id.toUpperCase()}.</span>
                                {option.text}
                              </Label>
                            </div>
                          ))}
                        </RadioGroup>
                      )}

                      {/* 判断题 */}
                      {question.type === 'true_false' && (
                        <RadioGroup
                          value={userAnswer as string || ''}
                          onValueChange={(value) => handleAnswerChange(question.id, value)}
                          className="space-y-3"
                        >
                          <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-gray-50 transition-colors cursor-pointer">
                            <RadioGroupItem value="true" id={`${question.id}-true`} />
                            <Label htmlFor={`${question.id}-true`} className="flex-1 cursor-pointer">True</Label>
                          </div>
                          <div className="flex items-center space-x-3 p-4 rounded-lg border hover:bg-gray-50 transition-colors cursor-pointer">
                            <RadioGroupItem value="false" id={`${question.id}-false`} />
                            <Label htmlFor={`${question.id}-false`} className="flex-1 cursor-pointer">False</Label>
                          </div>
                        </RadioGroup>
                      )}

                      {/* 简答题 */}
                      {question.type === 'short_answer' && (
                        <div className="space-y-2">
                          <Label htmlFor={`answer-${question.id}`}>Please enter your answer:</Label>
                          <Textarea
                            id={`answer-${question.id}`}
                            placeholder="Please enter your answer here..."
                            value={userAnswer as string || ''}
                            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                            className="min-h-[120px]"
                          />
                        </div>
                      )}
                    </>
                  )}

                  {/* 结果模式：显示答题分析 */}
                  {mode === 'results' && resultData && (
                    <div className="space-y-4">
                      {/* 用户答案 */}
                      <div className={`p-4 rounded-lg border-2 ${
                        resultData.isCorrect 
                          ? 'border-green-200 bg-green-50' 
                          : 'border-red-200 bg-red-50'
                      }`}>
                        <p className="text-sm font-medium text-muted-foreground mb-2">
                          Your answer:
                        </p>
                        <p className={`font-medium ${
                          resultData.isCorrect ? 'text-green-800' : 'text-red-800'
                        }`}>
                          {resultData.userAnswer}
                        </p>
                      </div>

                      {/* 正确答案（仅在答错时显示） */}
                      {!resultData.isCorrect && (
                        <div className="p-4 rounded-lg border-2 border-green-200 bg-green-50">
                          <p className="text-sm font-medium text-muted-foreground mb-2">
                            Correct answer:
                          </p>
                          <p className="font-medium text-green-800">
                            {resultData.correctAnswer}
                          </p>
                        </div>
                      )}

                      {/* 选择题选项展示 */}
                      {question.type === 'mcq' && question.options && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-muted-foreground">All options:</p>
                          {question.options.map((option) => {
                            const isUserChoice = option.id === attempt?.answers[question.id];
                            const isCorrectOption = option.isCorrect;
                            
                            return (
                              <div 
                                key={option.id} 
                                className={`p-3 rounded-lg border ${
                                  isCorrectOption 
                                    ? 'border-green-300 bg-green-100' 
                                    : isUserChoice && !isCorrectOption
                                    ? 'border-red-300 bg-red-100'
                                    : 'border-gray-200 bg-gray-50'
                                }`}
                              >
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium">{option.id.toUpperCase()}.</span>
                                  <span>{option.text}</span>
                                  <div className="flex items-center space-x-1 ml-auto">
                                    {isUserChoice && (
                                      <Badge variant="outline" className="text-xs">
                                        Your choice
                                      </Badge>
                                    )}
                                    {isCorrectOption && (
                                      <Badge variant="default" className="text-xs bg-green-600">
                                        Correct answer
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  )}

                  {/* 管理模式：显示标准答案和解释 */}
                  {mode === 'management' && (
                    <>
                      {/* 选择题选项 */}
                      {question.type === 'mcq' && question.options && (
                        <div className="space-y-2 mb-4">
                          <p className="text-sm font-medium text-muted-foreground">Options:</p>
                          {question.options.map((option) => (
                            <div 
                              key={option.id} 
                              className={`p-3 rounded-lg border ${
                                option.isCorrect 
                                  ? 'bg-green-50 border-green-200' 
                                  : 'bg-gray-50 border-gray-200'
                              }`}
                            >
                              <div className="flex items-center space-x-2">
                                <span className="font-medium">{option.id.toUpperCase()}.</span>
                                <span>{option.text}</span>
                                {option.isCorrect && (
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* 判断题答案 */}
                      {question.type === 'true_false' && question.correctAnswer && (
                        <div className="mb-4">
                          <p className="text-sm font-medium text-muted-foreground mb-2">Correct answer:</p>
                          <Badge variant={question.correctAnswer === 'true' ? 'default' : 'secondary'}>
                            {question.correctAnswer === 'true' ? 'True' : 'False'}
                          </Badge>
                        </div>
                      )}

                      {/* 简答题答案 */}
                      {question.type === 'short_answer' && question.correctAnswer && (
                        <div className="mb-4">
                          <p className="text-sm font-medium text-muted-foreground mb-2">Reference answer:</p>
                          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-sm">{question.correctAnswer}</p>
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  {/* 答案解释（仅在非答题模式下显示） */}
                  {mode !== 'taking' && question.explanation && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-muted-foreground mb-2">Explanation:</p>
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p className="text-sm">{question.explanation}</p>
                      </div>
                    </div>
                  )}

                  {/* 来源文本（仅在非答题模式下显示） */}
                  {mode !== 'taking' && question.sourceText && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-2">Source text:</p>
                      <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                        <p className="text-xs text-gray-600 italic">"{question.sourceText}"</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {/* 标签 */}
      {quiz.tags && quiz.tags.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm font-medium text-muted-foreground mb-2">Related tags:</p>
            <div className="flex flex-wrap gap-2">
              {quiz.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 