import { NextRequest } from 'next/server';
import { CreemService } from '@/services/creem';
import { insertOrder, updateCreemOrderSession } from '@/models/order';
import { Order } from '@/types/order';
import { respErr, respData } from '@/lib/resp';
import { getIsoTimestr, getOneYearLaterTimestr } from '@/lib/time';
import { getSnowId } from '@/lib/hash';

export async function POST(req: Request) {
  try {
    let {
      credits,
      currency,
      amount,
      interval,
      product_id,
      product_name,
      valid_months,
      cancel_url,
      user_email,
      user_uuid,
    } = await req.json();

    // 参数验证
    if (!cancel_url) {
      cancel_url = `${
        process.env.NEXT_PUBLIC_PAY_CANCEL_URL ||
        process.env.NEXT_PUBLIC_WEB_URL
      }`;
    }

    if (!amount || !interval || !currency || !product_id) {
      return respErr("invalid params");
    }

    if (!["year", "month", "one-time"].includes(interval)) {
      return respErr("invalid interval");
    }

    // 临时处理：免费版直接返回成功，不调用 Creem API
    if (amount === 0 && product_id === "free") {
      console.log("Processing free plan - skipping Creem API call");

      // 为免费版用户直接添加积分
      const order: Order = {
        order_no: order_no,
        created_at: created_at,
        user_uuid: user_uuid || "",
        user_email: user_email || "",
        amount: amount,
        interval: interval,
        expired_at: expired_at,
        status: "paid", // 免费版直接设为已支付
        credits: credits || 0,
        currency: currency,
        product_id: product_id,
        product_name: product_name,
        valid_months: valid_months,
      };

      await insertOrder(order);

      // 直接跳转到成功页面
      return respData({
        checkout_url: `${process.env.NEXT_PUBLIC_WEB_URL}/creem-success?checkout_id=free_${order_no}&order_id=${order_no}&product_id=${product_id}`,
        checkout_id: `free_${order_no}`,
        order_no: order_no,
        success_url: `${process.env.NEXT_PUBLIC_WEB_URL}/creem-success`,
      });
    }

    // 检查是否为订阅类型（当前 Creem.io 集成主要支持一次性支付）
    // const is_subscription = interval === "month" || interval === "year";

    if (interval === "year" && valid_months !== 12) {
      return respErr("invalid valid_months");
    }

    if (interval === "month" && valid_months !== 1) {
      return respErr("invalid valid_months");
    }

    if (!user_email && !user_uuid) {
      return respErr("user_email or user_uuid is required");
    }

    // 生成订单号
    const order_no = getSnowId();
    const created_at = getIsoTimestr();
    const expired_at = getOneYearLaterTimestr();

    // 映射到真正的 Creem 产品 ID
    const creemProductMap = {
      //free: process.env.FREE_PLAN_PRODUCT_ID!,
      plus: process.env.PLUS_PLAN_PRODUCT_ID!,
      pro: process.env.PRO_PLAN_PRODUCT_ID!,
      // year: process.env.YEAR_PLAN_PRODUCT_ID!,
    };

    const realProductId = creemProductMap[product_id] || product_id;

    // 创建订单对象
    const order: Order = {
      order_no: order_no,
      created_at: created_at,
      user_uuid: user_uuid || "",
      user_email: user_email || "",
      amount: amount,
      interval: interval,
      expired_at: expired_at,
      status: "created",
      credits: credits || 0,
      currency: currency,
      product_id: realProductId,
      product_name: product_name,
      valid_months: valid_months,
    };

    // 保存订单到数据库
    await insertOrder(order);

    // 初始化 Creem 服务
    const creemService = CreemService.getInstance();

    // 构建 Creem 支付会话参数
    const checkoutParams: any = {
      product_id: realProductId,
      request_id: order_no,
      success_url: `${process.env.NEXT_PUBLIC_WEB_URL}/creem-success`,
      metadata: {
        project: process.env.NEXT_PUBLIC_PROJECT_NAME || "",
        product_name: product_name,
        order_no: order_no.toString(),
        user_email: user_email,
        credits: credits,
        user_uuid: user_uuid,
        interval: interval,
        valid_months: valid_months,
        product_id,  // 保留简短标识，方便内部统计
      },
    };

    // 如果有用户邮箱，预填充客户信息
    if (user_email) {
      checkoutParams.customer = {
        email: user_email,
      };
    }

    console.log("Creating Creem checkout session with params:", JSON.stringify(checkoutParams));

    // 创建 Creem 支付会话
    const checkoutSession = await creemService.createCheckoutSession(checkoutParams);

    // 更新订单，保存 Creem 会话信息
    const order_detail = JSON.stringify(checkoutParams);
    await updateCreemOrderSession(order_no, checkoutSession.id, order_detail);

    // 返回支付会话信息
    return respData({
      checkout_url: checkoutSession.checkout_url,
      checkout_id: checkoutSession.id,
      order_no: order_no,
      success_url: checkoutSession.success_url,
    });

  } catch (e: any) {
    console.log("Creem checkout failed: ", e);
    return respErr("checkout failed: " + e.message);
  }
}

// 兼容性：支持 GET 请求获取支付会话信息
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const checkout_id = searchParams.get('checkout_id');

    if (!checkout_id) {
      return respErr("checkout_id is required");
    }

    const creemService = CreemService.getInstance();
    const checkoutSession = await creemService.getCheckoutSession(checkout_id);

    return respData({
      checkout_session: checkoutSession,
    });

  } catch (e: any) {
    console.log("Get Creem checkout session failed: ", e);
    return respErr("get checkout session failed: " + e.message);
  }
}
