import { QuotaAdapter } from '@/lib/quotaAdapter';

describe('QuotaAdapter', () => {
  describe('字符数与积分转换', () => {
    test('应该正确将字符数转换为积分数', () => {
      expect(QuotaAdapter.charactersToCredits(1000)).toBe(1);
      expect(QuotaAdapter.charactersToCredits(1500)).toBe(2); // 向上取整
      expect(QuotaAdapter.charactersToCredits(500)).toBe(1);   // 向上取整
      expect(QuotaAdapter.charactersToCredits(0)).toBe(0);
    });

    test('应该正确将积分数转换为字符数', () => {
      expect(QuotaAdapter.creditsToCharacters(1)).toBe(1000);
      expect(QuotaAdapter.creditsToCharacters(5)).toBe(5000);
      expect(QuotaAdapter.creditsToCharacters(0)).toBe(0);
    });
  });

  describe('配额显示信息', () => {
    test('应该为免费用户生成正确的配额显示信息', () => {
      const mockUser = {
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: {
          left_credits: 100,
          is_recharged: false
        }
      };

      const quotaInfo = QuotaAdapter.getQuotaDisplay(mockUser as any);

      expect(quotaInfo.plan).toBe('FREE');
      expect(quotaInfo.remainingCharacters).toBe(100000); // 100积分 * 1000字符
      expect(quotaInfo.totalCharacters).toBe(200000);     // 200积分 * 1000字符
      expect(quotaInfo.remainingCredits).toBe(100);
      expect(quotaInfo.pdfLimitPerUpload).toBe(5);        // 免费版单次5页限制
      expect(quotaInfo.displayText).toBe('100.0K / 200.0K characters');
    });

    test('应该为付费用户生成正确的配额显示信息', () => {
      const mockUser = {
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: {
          left_credits: 3000,
          is_recharged: true
        }
      };

      const quotaInfo = QuotaAdapter.getQuotaDisplay(mockUser as any);

      expect(quotaInfo.plan).toBe('PLUS');
      expect(quotaInfo.remainingCharacters).toBe(3000000); // 3000积分 * 1000字符
      expect(quotaInfo.totalCharacters).toBe(5000000);     // 5000积分 * 1000字符
      expect(quotaInfo.remainingCredits).toBe(3000);
      expect(quotaInfo.pdfLimitPerUpload).toBe(0);         // Plus版无单次限制
      expect(quotaInfo.maxPagesPerMonth).toBe(50);         // Plus版月度50页限制
      expect(quotaInfo.displayText).toBe('3000.0K / 5000.0K characters');
    });
  });

  describe('边界条件测试', () => {
    test('应该处理零积分的情况', () => {
      const mockUser = {
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: {
          left_credits: 0,
          is_recharged: false
        }
      };

      const quotaInfo = QuotaAdapter.getQuotaDisplay(mockUser as any);
      expect(quotaInfo.remainingCharacters).toBe(0);
      expect(quotaInfo.displayText).toBe('0.0K / 200.0K characters');
    });

    test('应该处理未定义积分的情况', () => {
      const mockUser = {
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: undefined
      };

      const quotaInfo = QuotaAdapter.getQuotaDisplay(mockUser as any);
      expect(quotaInfo.remainingCharacters).toBe(0);
      expect(quotaInfo.remainingCredits).toBe(0);
    });
  });
});
