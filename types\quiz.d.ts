// 测验相关类型定义

export interface QuizOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface QuizQuestion {
  id: string;
  text: string;
  type: 'mcq' | 'true_false' | 'short_answer' | 'essay';
  options?: QuizOption[];
  correctAnswer?: string;
  explanation?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  points?: number;
  sourceText?: string; // 来源PDF的相关文本
}

export interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  totalQuestions: number;
  totalPoints: number;
  estimatedTime?: number; // 预计完成时间（分钟）
  difficulty?: 'easy' | 'medium' | 'hard';
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
  sourceFile?: {
    name: string;
    type: string;
    size: number;
  };
}

export interface QuizGenerationOptions {
  questionCount: number;
  questionTypes: ('mcq' | 'true_false' | 'short_answer' | 'essay')[];
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  language: 'zh' | 'en';
  includeExplanations: boolean;
  focusAreas?: string[]; // 重点关注的主题
}

export interface QuizGenerationResult {
  quiz: Quiz;
  processingTime: number;
  extractedText: string;
  confidence: number; // AI生成的置信度
  warnings?: string[];
}

export interface QuizExportOptions {
  format: 'json' | 'pdf' | 'docx' | 'txt' | 'csv';
  includeAnswers: boolean;
  includeExplanations: boolean;
  template?: string;
}

export interface QuizAttempt {
  id: string;
  quizId: string;
  userId?: string;
  answers: Record<string, string | string[]>;
  score: number;
  totalPoints: number;
  percentage: number;
  timeSpent: number; // 秒
  startedAt: Date;
  completedAt: Date;
  isCompleted: boolean;
}

export interface QuizStats {
  totalAttempts: number;
  averageScore: number;
  averageTime: number;
  questionStats: {
    questionId: string;
    correctRate: number;
    averageTime: number;
  }[];
}

// 答题会话状态
export interface QuizSession {
  id: string;
  quizId: string;
  userId?: string;
  currentQuestionIndex: number;
  answers: Record<string, string | string[]>;
  flaggedQuestions: string[];
  startedAt: Date;
  timeSpent: number;
  isCompleted: boolean;
}

// 答题模式
export type QuizMode = 'taking' | 'review' | 'results';

// 答题界面配置
export interface QuizTakingConfig {
  allowNavigation: boolean;
  allowFlagging: boolean;
  showProgress: boolean;
  timeLimit?: number; // 秒
  shuffleQuestions: boolean;
  shuffleOptions: boolean;
}

// 在现有类型定义基础上添加
export interface QuizResultsEnhancedProps {
  quiz: Quiz;
  attempt?: QuizAttempt; // 新增：答题结果数据
  mode?: 'management' | 'results'; // 新增：显示模式
  onEdit?: (questionId: string) => void;
  onExport?: (format: string, selectedQuestions: string[]) => void;
  onShare?: () => void;
  onRetake?: () => void; // 新增：重新答题回调
}
