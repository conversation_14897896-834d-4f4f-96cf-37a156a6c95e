https://docs.creem.io/api-reference
API Documentation
Introduction
Checkout
POST
Create Checkout Session
GET
Get Checkout Session
Product
POST
Create Product
GET
Get Product
GET
List Products
Customer
GET
Get Customer
Transactions
GET
List Transactions
License
POST
Validate License Key
POST
Activate License Key
POST
Deactivate License Key
Discount Code
POST
Create Discount Code
DEL
Delete Discount Code
GET
Get Discount Code
Subscription
GET
Get Subscription
POST
Update Subscription
POST
Upgrade Subscription
POST
Cancel Subscription

--------------------
API Documentation
Introduction
Understand general concepts, response codes, and authentication strategies.

​
Base URL
The Creem API is built on REST principles. We enforce HTTPS in every request to improve data security, integrity, and privacy. The API does not support HTTP.

All requests contain the following base URL:


Copy
https://api.creem.io
​
Authentication
To authenticate you need to add an x-api-key header with the contents of the header being your API Key. All API endpoints are authenticated using Api Keys and picked up from the specification file.


Copy
  {
    headers: {
      "x-api-key": "creem_123456789"
    }
  }
​
Response codes
Creem uses standard HTTP codes to indicate the success or failure of your requests. In general, 2xx HTTP codes correspond to success, 4xx codes are for user-related failures, and 5xx codes are for infrastructure issues.

Status	Description
200	Successful request.
400	Check that the parameters were correct.
401	The API key used was missing.
403	The API key used was invalid.
404	The resource was not found.
429	The rate limit was exceeded.
429	Indicates an error with Creem servers.
------------


Checkout
Create Checkout Session
POST

https://api.creem.io
/
v1
/
checkouts

Try it
Headers
​
x-api-key
stringrequired
Body
application/json
​
product_id
stringrequired
The ID of the product associated with the checkout session.

​
request_id
string
Identify and track each checkout request.

​
units
number
The number of units for the order.

​
discount_code
string
Prefill the checkout session with a discount code.

​
customer
object
Customer data for checkout session. This will prefill the customer info on the checkout page


Show child attributes

​
custom_field
object[]
Collect additional information from your customer using custom fields. Up to 3 fields are supported.


Show child attributes

​
success_url
string
The URL to which the user will be redirected after the checkout process is completed.

​
metadata
object[]
A key-value pair where the key is a string, and the value can be a string, number, or null. This metadata will be propagated across all related objects, such as subscriptions and customers.

Response
200 - application/json
Successfully created a checkout session
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
status
stringrequired
Status of the checkout.

​
product
objectrequired
The product associated with the checkout session.

​
checkout_url
stringrequired
The URL to which the customer will be redirected to complete the payment.

​
success_url
stringrequired
The URL to which the user will be redirected after the checkout process is completed.

Example:
"https://example.com/return"

​
request_id
string
Identify and track each checkout request.

​
units
number
The number of units for the of the product.

​
order
object
The order associated with the checkout session.


Show child attributes

​
subscription
object
The subscription associated with the checkout session.

​
customer
object
The customer associated with the checkout session.

​
custom_fields
object[]
Additional information collected from your customer during the checkout process.


Show child attributes

​
feature
object[]
Features issued for the order.


Show child attributes

​
metadata
object[]
A key-value pair where the key is a string, and the value can be a string, number, or null.

const options = {
  method: 'POST',
  headers: {'x-api-key': '<x-api-key>', 'Content-Type': 'application/json'},
  body: '{"request_id":"<string>","product_id":"<string>","units":123,"discount_code":"<string>","customer":{"id":"<string>","email":"<EMAIL>"},"custom_field":[{"type":"text","key":"<string>","label":"<string>","optional":true,"text":{"max_length":123,"min_length":123}}],"success_url":"<string>","metadata":[{}]}'
};

fetch('https://api.creem.io/v1/checkouts', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "status": "<string>",
  "request_id": "<string>",
  "product": {},
  "units": 123,
  "order": {
    "id": "<string>",
    "mode": "test",
    "object": "<string>",
    "customer": {},
    "product": {},
    "amount": 2000,
    "currency": "EUR",
    "fx_amount": 15,
    "fx_currency": "EUR",
    "fx_rate": 1.2,
    "status": "pending",
    "type": "subscription",
    "affiliate": "<string>",
    "created_at": "2023-09-13T00:00:00Z",
    "updated_at": "2023-09-13T00:00:00Z"
  },
  "subscription": {},
  "customer": {},
  "custom_fields": [
    {
      "type": "<string>",
      "key": "<string>",
      "label": "<string>",
      "optional": true,
      "text": {
        "max_length": 123,
        "min_length": 123
      }
    }
  ],
  "checkout_url": "<string>",
  "success_url": "https://example.com/return",
  "feature": [
    {
      "license": {
        "id": "<string>",
        "mode": "test",
        "object": "<string>",
        "status": "active",
        "key": "ABC123-XYZ456-XYZ456-XYZ456",
        "activation": 5,
        "activation_limit": 1,
        "expires_at": "2023-09-13T00:00:00Z",
        "created_at": "2023-09-13T00:00:00Z",
        "instance": [
          {
            "id": "<string>",
            "mode": "test",
            "object": "license-instance",
            "name": "My Customer License Instance",
            "status": "active",
            "created_at": "2023-09-13T00:00:00Z"
          }
        ]
      }
    }
  ],
  "metadata": [
    {}
  ]
}
------
Checkout
Get Checkout Session
GET

https://api.creem.io
/
v1
/
checkouts

Try it
Headers
​
x-api-key
stringrequired
Query Parameters
​
checkout_id
stringrequired
Response
200 - application/json
Successfully retrieved the subscription
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
status
stringrequired
Status of the checkout.

​
product
objectrequired
The product associated with the checkout session.

​
checkout_url
stringrequired
The URL to which the customer will be redirected to complete the payment.

​
success_url
stringrequired
The URL to which the user will be redirected after the checkout process is completed.

Example:
"https://example.com/return"

​
request_id
string
Identify and track each checkout request.

​
units
number
The number of units for the of the product.

​
order
object
The order associated with the checkout session.


Show child attributes

​
subscription
object
The subscription associated with the checkout session.

​
customer
object
The customer associated with the checkout session.

​
custom_fields
object[]
Additional information collected from your customer during the checkout process.


Show child attributes

​
feature
object[]
Features issued for the order.


Show child attributes

​
metadata
object[]
A key-value pair where the key is a string, and the value can be a string, number, or null.

const options = {method: 'GET', headers: {'x-api-key': '<x-api-key>'}};

fetch('https://api.creem.io/v1/checkouts', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "status": "<string>",
  "request_id": "<string>",
  "product": {},
  "units": 123,
  "order": {
    "id": "<string>",
    "mode": "test",
    "object": "<string>",
    "customer": {},
    "product": {},
    "amount": 2000,
    "currency": "EUR",
    "fx_amount": 15,
    "fx_currency": "EUR",
    "fx_rate": 1.2,
    "status": "pending",
    "type": "subscription",
    "affiliate": "<string>",
    "created_at": "2023-09-13T00:00:00Z",
    "updated_at": "2023-09-13T00:00:00Z"
  },
  "subscription": {},
  "customer": {},
  "custom_fields": [
    {
      "type": "<string>",
      "key": "<string>",
      "label": "<string>",
      "optional": true,
      "text": {
        "max_length": 123,
        "min_length": 123
      }
    }
  ],
  "checkout_url": "<string>",
  "success_url": "https://example.com/return",
  "feature": [
    {
      "license": {
        "id": "<string>",
        "mode": "test",
        "object": "<string>",
        "status": "active",
        "key": "ABC123-XYZ456-XYZ456-XYZ456",
        "activation": 5,
        "activation_limit": 1,
        "expires_at": "2023-09-13T00:00:00Z",
        "created_at": "2023-09-13T00:00:00Z",
        "instance": [
          {
            "id": "<string>",
            "mode": "test",
            "object": "license-instance",
            "name": "My Customer License Instance",
            "status": "active",
            "created_at": "2023-09-13T00:00:00Z"
          }
        ]
      }
    }
  ],
  "metadata": [
    {}
  ]
}

------------
Product
Create Product
POST

https://api.creem.io
/
v1
/
products

Try it
Headers
​
x-api-key
stringrequired
Body
application/json
​
name
stringrequired
Name of the product

​
price
integerrequired
The price of the product in cents

Required range: x >= 100
Example:
400

​
currency
stringrequired
Three-letter ISO currency code, in uppercase. Must be a supported currency.

Example:
"EUR"

​
billing_type
stringrequired
Indicates the billing method for the customer. It can either be a recurring billing cycle or a onetime payment.

Example:
"recurring"

​
description
string
Description of the product

​
image_url
string
URL of the product image

Example:
"https://picsum.photos/200/300"

​
billing_period
string
Billing period, required if billing_type is recurring

Example:
"every-month"

​
features
object[]
A list of features fpr the product.


Show child attributes

​
tax_mode
string
Specifies the tax calculation mode for the transaction. If set to "inclusive," the tax is included in the price. If set to "exclusive," the tax is added on top of the price.

Example:
"inclusive"

​
tax_category
string
Categorizes the type of product or service for tax purposes. This helps determine the applicable tax rules based on the nature of the item or service.

Example:
"saas"

​
default_success_url
string
The URL to which the user will be redirected after successfull payment.

Example:
"https://example.com/?status=successful"

​
custom_field
object[]
Collect additional information from your customer using custom fields during checkout. Up to 3 fields are supported.


Show child attributes

Response
200 - application/json
Successfully created a product
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
name
stringrequired
The name of the product

​
description
stringrequired
A brief description of the product

Example:
"This is a sample product description."

​
price
numberrequired
The price of the product in cents. 1000 = $10.00

Example:
400

​
currency
stringrequired
Three-letter ISO currency code, in uppercase. Must be a supported currency.

Example:
"EUR"

​
billing_type
stringrequired
Indicates the billing method for the customer. It can either be a recurring billing cycle or a onetime payment.

Example:
"recurring"

​
billing_period
stringrequired
Billing period

Example:
"every-month"

​
status
stringrequired
Status of the product

​
tax_mode
stringrequired
Specifies the tax calculation mode for the transaction. If set to "inclusive," the tax is included in the price. If set to "exclusive," the tax is added on top of the price.

Example:
"inclusive"

​
tax_category
stringrequired
Categorizes the type of product or service for tax purposes. This helps determine the applicable tax rules based on the nature of the item or service.

Example:
"saas"

​
product_url
stringrequired
The product page you can redirect your customers to for express checkout.

Example:
"https://creem.io/product/prod_123123123123"

​
created_at
stringrequired
Creation date of the product

Example:
"2023-01-01T00:00:00Z"

​
updated_at
stringrequired
Last updated date of the product

Example:
"2023-01-01T00:00:00Z"

​
image_url
string
URL of the product image. Only png as jpg are supported

Example:
"https://example.com/image.jpg"

​
features
object[]
Features of the product.


Show child attributes

​
default_success_url
string
The URL to which the user will be redirected after successfull payment.

Example:
"https://example.com/?status=successful"

const options = {
  method: 'POST',
  headers: {'x-api-key': '<x-api-key>', 'Content-Type': 'application/json'},
  body: '{"name":"<string>","description":"<string>","image_url":"https://picsum.photos/200/300","price":400,"currency":"EUR","billing_type":"recurring","billing_period":"every-month","features":[{"id":"<string>","type":"<string>","description":"Get access to discord server."}],"tax_mode":"inclusive","tax_category":"saas","default_success_url":"https://example.com/?status=successful","custom_field":[{"type":"text","key":"<string>","label":"<string>","optional":true,"text":{"max_length":123,"min_length":123}}]}'
};

fetch('https://api.creem.io/v1/products', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "name": "<string>",
  "description": "This is a sample product description.",
  "image_url": "https://example.com/image.jpg",
  "features": [
    {
      "id": "<string>",
      "type": "<string>",
      "description": "Get access to discord server."
    }
  ],
  "price": 400,
  "currency": "EUR",
  "billing_type": "recurring",
  "billing_period": "every-month",
  "status": "<string>",
  "tax_mode": "inclusive",
  "tax_category": "saas",
  "product_url": "https://creem.io/product/prod_123123123123",
  "default_success_url": "https://example.com/?status=successful",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}

------
Product
Get Product
GET

https://api.creem.io
/
v1
/
products

Try it
Headers
​
x-api-key
stringrequired
Query Parameters
​
product_id
stringrequired
The unique identifier of the product

Response
200 - application/json
Successfully retrieved the product
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
name
stringrequired
The name of the product

​
description
stringrequired
A brief description of the product

Example:
"This is a sample product description."

​
price
numberrequired
The price of the product in cents. 1000 = $10.00

Example:
400

​
currency
stringrequired
Three-letter ISO currency code, in uppercase. Must be a supported currency.

Example:
"EUR"

​
billing_type
stringrequired
Indicates the billing method for the customer. It can either be a recurring billing cycle or a onetime payment.

Example:
"recurring"

​
billing_period
stringrequired
Billing period

Example:
"every-month"

​
status
stringrequired
Status of the product

​
tax_mode
stringrequired
Specifies the tax calculation mode for the transaction. If set to "inclusive," the tax is included in the price. If set to "exclusive," the tax is added on top of the price.

Example:
"inclusive"

​
tax_category
stringrequired
Categorizes the type of product or service for tax purposes. This helps determine the applicable tax rules based on the nature of the item or service.

Example:
"saas"

​
product_url
stringrequired
The product page you can redirect your customers to for express checkout.

Example:
"https://creem.io/product/prod_123123123123"

​
created_at
stringrequired
Creation date of the product

Example:
"2023-01-01T00:00:00Z"

​
updated_at
stringrequired
Last updated date of the product

Example:
"2023-01-01T00:00:00Z"

​
image_url
string
URL of the product image. Only png as jpg are supported

Example:
"https://example.com/image.jpg"

​
features
object[]
Features of the product.


Show child attributes

​
default_success_url
string
The URL to which the user will be redirected after successfull payment.

Example:
"https://example.com/?status=successful"

const options = {method: 'GET', headers: {'x-api-key': '<x-api-key>'}};

fetch('https://api.creem.io/v1/products', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "name": "<string>",
  "description": "This is a sample product description.",
  "image_url": "https://example.com/image.jpg",
  "features": [
    {
      "id": "<string>",
      "type": "<string>",
      "description": "Get access to discord server."
    }
  ],
  "price": 400,
  "currency": "EUR",
  "billing_type": "recurring",
  "billing_period": "every-month",
  "status": "<string>",
  "tax_mode": "inclusive",
  "tax_category": "saas",
  "product_url": "https://creem.io/product/prod_123123123123",
  "default_success_url": "https://example.com/?status=successful",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}

----
Product
List Products
GET

https://api.creem.io
/
v1
/
products
/
search

Try it
Headers
​
x-api-key
stringrequired
Query Parameters
​
page_number
number
The page number

​
page_size
number
The the page size

Response
200 - application/json
Successfully retrieved products
​
items
object[]required
List of product items


Show child attributes

​
pagination
objectrequired
Pagination details for the list


Show child attributes

{
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "name": "<string>",
      "description": "This is a sample product description.",
      "image_url": "https://example.com/image.jpg",
      "features": [
        {
          "id": "<string>",
          "type": "<string>",
          "description": "Get access to discord server."
        }
      ],
      "price": 400,
      "currency": "EUR",
      "billing_type": "recurring",
      "billing_period": "every-month",
      "status": "<string>",
      "tax_mode": "inclusive",
      "tax_category": "saas",
      "product_url": "https://creem.io/product/prod_123123123123",
      "default_success_url": "https://example.com/?status=successful",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total_records": 0,
    "total_pages": 0,
    "current_page": 1,
    "next_page": 2,
    "prev_page": null
  }
}

200
{
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "name": "<string>",
      "description": "This is a sample product description.",
      "image_url": "https://example.com/image.jpg",
      "features": [
        {
          "id": "<string>",
          "type": "<string>",
          "description": "Get access to discord server."
        }
      ],
      "price": 400,
      "currency": "EUR",
      "billing_type": "recurring",
      "billing_period": "every-month",
      "status": "<string>",
      "tax_mode": "inclusive",
      "tax_category": "saas",
      "product_url": "https://creem.io/product/prod_123123123123",
      "default_success_url": "https://example.com/?status=successful",
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total_records": 0,
    "total_pages": 0,
    "current_page": 1,
    "next_page": 2,
    "prev_page": null
  }
}

---
Customer
Get Customer
GET

https://api.creem.io
/
v1
/
customers

Try it
Headers
​
x-api-key
stringrequired
Query Parameters
​
customer_id
string
The unique identifier of the customer

​
email
string
The unique email of the customer

Response
200 - application/json
Successfully retrieved the customer
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
email
stringrequired
Customer email address.

Example:
"<EMAIL>"

​
country
stringrequired
The ISO alpha-2 country code for the customer.

Example:
"US"

​
created_at
stringrequired
Creation date of the product

Example:
"2023-01-01T00:00:00Z"

​
updated_at
stringrequired
Last updated date of the product

Example:
"2023-01-01T00:00:00Z"

​
name
string
Customer name.

Example:
"John Doe"

{
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "email": "<EMAIL>",
  "name": "John Doe",
  "country": "US",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}

200
{
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "email": "<EMAIL>",
  "name": "John Doe",
  "country": "US",
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}

----
Transactions
List Transactions
GET

https://api.creem.io
/
v1
/
transactions
/
search

Try it
Headers
​
x-api-key
stringrequired
Query Parameters
​
customer_id
string
The customer id

​
order_id
string
The order id

​
product_id
string
The product id

​
page_number
number
The page number

​
page_size
number
The the page size

Response
200 - application/json
Successfully retrieved transactions
​
items
object[]required
List of transactions items


Show child attributes

​
pagination
objectrequired
Pagination details for the list


Show child attributes

const options = {method: 'GET', headers: {'x-api-key': '<x-api-key>'}};

fetch('https://api.creem.io/v1/transactions/search', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "amount": 2000,
      "amount_paid": 2000,
      "currency": "EUR",
      "type": "<string>",
      "tax_country": "US",
      "tax_amount": 2000,
      "status": "<string>",
      "refunded_amount": 2000,
      "order": {},
      "subscription": {},
      "customer": {},
      "description": "<string>",
      "period_start": 123,
      "period_end": 123,
      "created_at": 123
    }
  ],
  "pagination": {
    "total_records": 0,
    "total_pages": 0,
    "current_page": 1,
    "next_page": 2,
    "prev_page": null
  }
}

-----
License
POST
Validate License Key
POST
Activate License Key
POST
Deactivate License Key
Discount Code
POST
Create Discount Code
DEL
Delete Discount Code
GET
Get Discount Code

----
Subscription
Get Subscription
GET

https://api.creem.io
/
v1
/
subscriptions

Try it
Headers
​
x-api-key
stringrequired
Query Parameters
​
subscription_id
stringrequired
The unique identifier of the subscription

Response
200 - application/json
Successfully retrieved the subscription
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
product
objectrequired
The product associated with the subscription.

​
customer
objectrequired
The customer who owns the subscription.

​
collection_method
stringrequired
The method used for collecting payments for the subscription.

Example:
"charge_automatically"

​
status
enum<string>required
The current status of the subscription.

Available options: active, canceled, unpaid, paused, trialing 
Example:
"active"

​
created_at
stringrequired
The date and time when the subscription was created.

Example:
"2024-01-01T00:00:00Z"

​
updated_at
stringrequired
The date and time when the subscription was last updated.

Example:
"2024-09-12T12:34:56Z"

​
items
object[]
Subscription items.


Show child attributes

​
last_transaction_id
string
The ID of the last paid transaction.

Example:
"tran_3e6Z6TzvHKdsjEgXnGDEp0"

​
last_transaction_date
string
The date of the last paid transaction.

Example:
"2024-09-12T12:34:56Z"

​
next_transaction_date
string
The date when the next subscription transaction will be charged.

Example:
"2024-09-12T12:34:56Z"

​
current_period_start_date
string
The start date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
current_period_end_date
string
The end date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
canceled_at
string
The date and time when the subscription was canceled, if applicable.

Example:
"2024-09-12T12:34:56Z"

{
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "product": {},
  "customer": {},
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "product_id": "<string>",
      "price_id": "<string>",
      "units": 123
    }
  ],
  "collection_method": "charge_automatically",
  "status": "active",
  "last_transaction_id": "tran_3e6Z6TzvHKdsjEgXnGDEp0",
  "last_transaction_date": "2024-09-12T12:34:56Z",
  "next_transaction_date": "2024-09-12T12:34:56Z",
  "current_period_start_date": "2024-09-12T12:34:56Z",
  "current_period_end_date": "2024-09-12T12:34:56Z",
  "canceled_at": "2024-09-12T12:34:56Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-09-12T12:34:56Z"
}

200
{
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "product": {},
  "customer": {},
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "product_id": "<string>",
      "price_id": "<string>",
      "units": 123
    }
  ],
  "collection_method": "charge_automatically",
  "status": "active",
  "last_transaction_id": "tran_3e6Z6TzvHKdsjEgXnGDEp0",
  "last_transaction_date": "2024-09-12T12:34:56Z",
  "next_transaction_date": "2024-09-12T12:34:56Z",
  "current_period_start_date": "2024-09-12T12:34:56Z",
  "current_period_end_date": "2024-09-12T12:34:56Z",
  "canceled_at": "2024-09-12T12:34:56Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-09-12T12:34:56Z"
}
---
Subscription
Update Subscription
POST

https://api.creem.io
/
v1
/
subscriptions
/
{id}

Try it
Headers
​
x-api-key
stringrequired
Path Parameters
​
id
stringrequired
Body
application/json
​
items
object[]
List of subscription items to update/create. If no item ID is provided, the item will be created.


Show child attributes

​
update_behavior
enum<string>default:proration-charge
The update behavior for the subscription (defaults to proration)

Available options: proration-charge-immediately, proration-charge, proration-none 
Response
200 - application/json
Successfully update a subscription
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
product
objectrequired
The product associated with the subscription.

​
customer
objectrequired
The customer who owns the subscription.

​
collection_method
stringrequired
The method used for collecting payments for the subscription.

Example:
"charge_automatically"

​
status
enum<string>required
The current status of the subscription.

Available options: active, canceled, unpaid, paused, trialing 
Example:
"active"

​
created_at
stringrequired
The date and time when the subscription was created.

Example:
"2024-01-01T00:00:00Z"

​
updated_at
stringrequired
The date and time when the subscription was last updated.

Example:
"2024-09-12T12:34:56Z"

​
items
object[]
Subscription items.


Show child attributes

​
last_transaction_id
string
The ID of the last paid transaction.

Example:
"tran_3e6Z6TzvHKdsjEgXnGDEp0"

​
last_transaction_date
string
The date of the last paid transaction.

Example:
"2024-09-12T12:34:56Z"

​
next_transaction_date
string
The date when the next subscription transaction will be charged.

Example:
"2024-09-12T12:34:56Z"

​
current_period_start_date
string
The start date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
current_period_end_date
string
The end date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
canceled_at
string
The date and time when the subscription was canceled, if applicable.

Example:
"2024-09-12T12:34:56Z"

const options = {
  method: 'POST',
  headers: {'x-api-key': '<x-api-key>', 'Content-Type': 'application/json'},
  body: '{"items":[{"id":"<string>","product_id":"<string>","price_id":"<string>","units":123}],"update_behavior":"proration-charge"}'
};

fetch('https://api.creem.io/v1/subscriptions/{id}', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "product": {},
  "customer": {},
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "product_id": "<string>",
      "price_id": "<string>",
      "units": 123
    }
  ],
  "collection_method": "charge_automatically",
  "status": "active",
  "last_transaction_id": "tran_3e6Z6TzvHKdsjEgXnGDEp0",
  "last_transaction_date": "2024-09-12T12:34:56Z",
  "next_transaction_date": "2024-09-12T12:34:56Z",
  "current_period_start_date": "2024-09-12T12:34:56Z",
  "current_period_end_date": "2024-09-12T12:34:56Z",
  "canceled_at": "2024-09-12T12:34:56Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-09-12T12:34:56Z"
}
----
Subscription
Upgrade Subscription
POST

https://api.creem.io
/
v1
/
subscriptions
/
{id}
/
upgrade

Try it
Headers
​
x-api-key
stringrequired
Path Parameters
​
id
stringrequired
Body
application/json
​
product_id
stringrequired
The ID of the product to upgrade to

Example:
"prod_123"

​
update_behavior
enum<string>default:proration-charge-immediately
The update behavior for the subscription (defaults to proration-charge-immediately)

Available options: proration-charge-immediately, proration-charge, proration-none 
Response
200 - application/json
Successfully upgraded the subscription
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
product
objectrequired
The product associated with the subscription.

​
customer
objectrequired
The customer who owns the subscription.

​
collection_method
stringrequired
The method used for collecting payments for the subscription.

Example:
"charge_automatically"

​
status
enum<string>required
The current status of the subscription.

Available options: active, canceled, unpaid, paused, trialing 
Example:
"active"

​
created_at
stringrequired
The date and time when the subscription was created.

Example:
"2024-01-01T00:00:00Z"

​
updated_at
stringrequired
The date and time when the subscription was last updated.

Example:
"2024-09-12T12:34:56Z"

​
items
object[]
Subscription items.


Show child attributes

​
last_transaction_id
string
The ID of the last paid transaction.

Example:
"tran_3e6Z6TzvHKdsjEgXnGDEp0"

​
last_transaction_date
string
The date of the last paid transaction.

Example:
"2024-09-12T12:34:56Z"

​
next_transaction_date
string
The date when the next subscription transaction will be charged.

Example:
"2024-09-12T12:34:56Z"

​
current_period_start_date
string
The start date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
current_period_end_date
string
The end date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
canceled_at
string
The date and time when the subscription was canceled, if applicable.

Example:
"2024-09-12T12:34:56Z"

const options = {
  method: 'POST',
  headers: {'x-api-key': '<x-api-key>', 'Content-Type': 'application/json'},
  body: '{"product_id":"prod_123","update_behavior":"proration-charge-immediately"}'
};

fetch('https://api.creem.io/v1/subscriptions/{id}/upgrade', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "product": {},
  "customer": {},
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "product_id": "<string>",
      "price_id": "<string>",
      "units": 123
    }
  ],
  "collection_method": "charge_automatically",
  "status": "active",
  "last_transaction_id": "tran_3e6Z6TzvHKdsjEgXnGDEp0",
  "last_transaction_date": "2024-09-12T12:34:56Z",
  "next_transaction_date": "2024-09-12T12:34:56Z",
  "current_period_start_date": "2024-09-12T12:34:56Z",
  "current_period_end_date": "2024-09-12T12:34:56Z",
  "canceled_at": "2024-09-12T12:34:56Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-09-12T12:34:56Z"
}
---

Subscription
Cancel Subscription
POST

https://api.creem.io
/
v1
/
subscriptions
/
{id}
/
cancel

Try it
Headers
​
x-api-key
stringrequired
Path Parameters
​
id
stringrequired
Response
200 - application/json
Successfully canceled a subscription
​
id
stringrequired
Unique identifier for the object.

​
mode
enum<string>required
String representing the environment.

Available options: test, live, sandbox 
​
object
stringrequired
String representing the object’s type. Objects of the same type share the same value.

​
product
objectrequired
The product associated with the subscription.

​
customer
objectrequired
The customer who owns the subscription.

​
collection_method
stringrequired
The method used for collecting payments for the subscription.

Example:
"charge_automatically"

​
status
enum<string>required
The current status of the subscription.

Available options: active, canceled, unpaid, paused, trialing 
Example:
"active"

​
created_at
stringrequired
The date and time when the subscription was created.

Example:
"2024-01-01T00:00:00Z"

​
updated_at
stringrequired
The date and time when the subscription was last updated.

Example:
"2024-09-12T12:34:56Z"

​
items
object[]
Subscription items.


Show child attributes

​
last_transaction_id
string
The ID of the last paid transaction.

Example:
"tran_3e6Z6TzvHKdsjEgXnGDEp0"

​
last_transaction_date
string
The date of the last paid transaction.

Example:
"2024-09-12T12:34:56Z"

​
next_transaction_date
string
The date when the next subscription transaction will be charged.

Example:
"2024-09-12T12:34:56Z"

​
current_period_start_date
string
The start date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
current_period_end_date
string
The end date of the current subscription period.

Example:
"2024-09-12T12:34:56Z"

​
canceled_at
string
The date and time when the subscription was canceled, if applicable.

Example:
"2024-09-12T12:34:56Z"

const options = {method: 'POST', headers: {'x-api-key': '<x-api-key>'}};

fetch('https://api.creem.io/v1/subscriptions/{id}/cancel', options)
  .then(response => response.json())
  .then(response => console.log(response))
  .catch(err => console.error(err));

  200
  {
  "id": "<string>",
  "mode": "test",
  "object": "<string>",
  "product": {},
  "customer": {},
  "items": [
    {
      "id": "<string>",
      "mode": "test",
      "object": "<string>",
      "product_id": "<string>",
      "price_id": "<string>",
      "units": 123
    }
  ],
  "collection_method": "charge_automatically",
  "status": "active",
  "last_transaction_id": "tran_3e6Z6TzvHKdsjEgXnGDEp0",
  "last_transaction_date": "2024-09-12T12:34:56Z",
  "next_transaction_date": "2024-09-12T12:34:56Z",
  "current_period_start_date": "2024-09-12T12:34:56Z",
  "current_period_end_date": "2024-09-12T12:34:56Z",
  "canceled_at": "2024-09-12T12:34:56Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-09-12T12:34:56Z"
}
