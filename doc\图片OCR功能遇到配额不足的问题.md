图片OCR功能遇到配额不足的问题！这是因为图片OCR处理通常需要更多的字符配额。让我帮你分析和解决这个问题。

Read file: services/quotaService.ts

Read file: app/api/pdf/convert-to-quiz/route.ts
我发现问题了！OCR图片处理的字符数估算算法有问题。图片OCR通常会产生大量文字，但当前的估算算法是基于PDF页数的，对图片不适用。让我修复这个问题：

## 🔧 **解决方案：优化图片OCR的配额估算**

### **问题分析：**
1. **当前算法：** `estimatedPages * 1000` - 对图片来说估算过高
2. **实际情况：** 图片OCR提取的文字通常较少（除非是密集文本）
3. **解决方法：** 为图片文件提供更精确的字符估算

### **修改配额服务：**

```typescript
// services/quotaService.ts - 更新字符估算方法
/**
 * 估算文件字符数（根据文件类型优化算法）
 * @param fileType 文件MIME类型
 * @param fileSize 文件大小（字节）
 * @param pagesCount 页数（对PDF有效）
 * @returns 估算的字符数
 */
static estimateFileCharacters(fileType: string, fileSize: number, pagesCount?: number): number {
  // PDF文件：基于页数估算
  if (fileType === 'application/pdf') {
    return (pagesCount || Math.ceil(fileSize / 50000)) * 1000;
  }
  
  // DOCX文件：基于文件大小估算（Word文档通常字符密度较高）
  if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return Math.ceil(fileSize / 20); // 每20字节约1个字符（考虑XML结构）
  }
  
  // 图片文件：OCR估算（通常字符数较少）
  if (fileType.startsWith('image/')) {
    // 图片OCR估算算法：根据图片大小和类型
    const baseSizeKB = fileSize / 1024;
    
    if (baseSizeKB < 100) {
      return 200; // 小图片，估算200字符
    } else if (baseSizeKB < 500) {
      return 500; // 中等图片，估算500字符  
    } else if (baseSizeKB < 2000) {
      return 1000; // 大图片，估算1000字符
    } else {
      return 1500; // 超大图片，估算1500字符
    }
  }
  
  // 文本文件：直接基于文件大小
  if (fileType === 'text/plain') {
    return fileSize; // 文本文件每字节约等于1字符
  }
  
  // 默认估算
  return Math.ceil(fileSize / 10);
}

/**
 * 估算PDF字符数（保持向后兼容）
 */
static estimatePdfCharacters(pagesCount: number, fileSize?: number): number {
  return this.estimateFileCharacters('application/pdf', fileSize || pagesCount * 50000, pagesCount);
}
```

### **修改API路由：**

```typescript
// app/api/pdf/convert-to-quiz/route.ts - 更新配额检查逻辑
// 在配额检查部分替换为：

// 配额检查 - 根据文件类型优化估算
let estimatedCharacters: number;
let estimatedPages: number;

if (file.type === 'application/pdf') {
  estimatedPages = Math.ceil(file.size / 50000);
  estimatedCharacters = QuotaService.estimatePdfCharacters(estimatedPages, file.size);
} else if (file.type.startsWith('image/')) {
  estimatedPages = 1; // 图片视为1页
  estimatedCharacters = QuotaService.estimateFileCharacters(file.type, file.size);
} else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
  estimatedPages = Math.max(1, Math.ceil(file.size / 100000)); // Word文档页数估算
  estimatedCharacters = QuotaService.estimateFileCharacters(file.type, file.size);
} else {
  estimatedPages = 1;
  estimatedCharacters = QuotaService.estimateFileCharacters(file.type, file.size);
}

console.log(`File type: ${file.type}, Size: ${file.size}, Estimated chars: ${estimatedCharacters}`);
```

### **为图片处理添加特殊处理：**

```typescript
// services/pdfService.ts - 优化图片OCR处理
/**
 * 提取图片内容（使用OCR）- 优化版本
 */
private async extractImageContent(file: File): Promise<PDFTextContent> {
  try {
    console.log(`开始OCR处理: ${file.name}, 大小: ${file.size} bytes`);
    
    // 对大图片进行预处理以提高OCR效率
    let processFile = file;
    if (file.size > 5 * 1024 * 1024) { // 5MB以上的图片
      console.log('Large image detected, consider optimization for better performance');
    }
    
    // 使用Tesseract进行OCR识别，添加进度监控
    const { data: { text, confidence } } = await Tesseract.recognize(
      processFile, 
      'eng+chi_sim',
      {
        logger: m => {
          if (m.status === 'recognizing text') {
            console.log(`OCR进度: ${Math.round(m.progress * 100)}%`);
          }
        }
      }
    );
    
    console.log(`OCR完成: 提取了 ${text.length} 个字符, 置信度: ${confidence}%`);
    
    // 如果提取的文字太少，给出提示
    if (text.length < 50) {
      console.warn('OCR extracted very little text, image may not contain readable text');
    }
    
    const pages: PDFPageContent[] = [{
      pageNumber: 1,
      text: text,
      wordCount: this.countWords(text),
      hasImages: true,
      confidence: confidence / 100 // Tesseract返回0-100，我们转换为0-1
    }];

    const metadata: PDFMetadata = {
      title: file.name,
      pageCount: 1,
      fileSize: file.size,
      isEncrypted: false,
      hasFormFields: false
    };

    return {
      text,
      pages,
      metadata,
      processingTime: 0,
      wordCount: this.countWords(text),
      characterCount: text.length
    };
  } catch (error) {
    console.error('OCR processing error:', error);
    throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
```

## 🎯 **立即测试方案**

如果你想快速测试，可以临时降低图片的配额消耗：

```typescript
// 临时修复 - 在 services/quotaService.ts 中
static estimateFileCharacters(fileType: string, fileSize: number, pagesCount?: number): number {
  // 临时：图片文件使用最小配额
  if (fileType.startsWith('image/')) {
    return 100; // 临时设为100字符，便于测试
  }
  
  // ... 其他逻辑保持不变
}
```

## 📊 **优化后的配额消耗对比**

| 文件类型 | 原估算方式 | 新估算方式 | 节省配额 |
|---------|------------|------------|----------|
| 1MB图片 | ~20,000字符 | 500-1000字符 | 95%+ |
| 500KB图片 | ~10,000字符 | 200-500字符 | 95%+ |
| 100KB图片 | ~2,000字符 | 200字符 | 90%+ |

## 🚀 **实施步骤**

1. **更新quotaService.ts** - 添加新的估算方法
2. **更新convert-to-quiz API** - 使用新的估算逻辑  
3. **测试图片上传** - 验证配额消耗合理

这样修改后，图片OCR的配额消耗会大幅降低，用户就可以正常使用图片转测验功能了！

你希望我立即提供完整的修改代码吗？

