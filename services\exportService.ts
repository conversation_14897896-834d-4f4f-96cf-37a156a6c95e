import { Quiz, QuizQuestion } from '@/types/quiz';

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'txt' | 'json';
  selectedQuestions: string[];
  includeAnswers: boolean;
  includeExplanations: boolean;
  template?: 'standard' | 'exam' | 'practice';
}

export class ExportService {
  private static instance: ExportService;

  public static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService();
    }
    return ExportService.instance;
  }

  /**
   * 导出测验为指定格式
   */
  public async exportQuiz(
    quiz: Quiz, 
    options: ExportOptions
  ): Promise<{ success: boolean; downloadUrl?: string; error?: string }> {
    try {
      // 过滤选中的题目
      const selectedQuestions = quiz.questions.filter(q => 
        options.selectedQuestions.includes(q.id)
      );

      if (selectedQuestions.length === 0) {
        throw new Error('没有选择任何题目进行导出');
      }

      // 根据格式调用相应的导出方法
      switch (options.format) {
        case 'pdf':
          return await this.exportToPDF(quiz, selectedQuestions, options);
        case 'docx':
          return await this.exportToWord(quiz, selectedQuestions, options);
        case 'txt':
          return await this.exportToText(quiz, selectedQuestions, options);
        case 'json':
          return await this.exportToJSON(quiz, selectedQuestions, options);
        default:
          throw new Error(`不支持的导出格式: ${options.format}`);
      }
    } catch (error) {
      console.error('导出失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      };
    }
  }

  /**
   * 导出为PDF格式
   */
  private async exportToPDF(
    quiz: Quiz, 
    questions: QuizQuestion[], 
    options: ExportOptions
  ): Promise<{ success: boolean; downloadUrl?: string; error?: string }> {
    try {
      const response = await fetch('/api/export/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quiz: {
            ...quiz,
            questions
          },
          options
        }),
      });

      if (!response.ok) {
        throw new Error('PDF导出请求失败');
      }

      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      
      // 触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${quiz.title || 'quiz'}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return { success: true, downloadUrl };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'PDF导出失败'
      };
    }
  }

  /**
   * 导出为Word格式
   */
  private async exportToWord(
    quiz: Quiz, 
    questions: QuizQuestion[], 
    options: ExportOptions
  ): Promise<{ success: boolean; downloadUrl?: string; error?: string }> {
    try {
      const response = await fetch('/api/export/docx', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quiz: {
            ...quiz,
            questions
          },
          options
        }),
      });

      if (!response.ok) {
        throw new Error('Word导出请求失败');
      }

      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      
      // 触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${quiz.title || 'quiz'}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return { success: true, downloadUrl };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Word导出失败'
      };
    }
  }

  /**
   * 导出为文本格式
   */
  private async exportToText(
    quiz: Quiz, 
    questions: QuizQuestion[], 
    options: ExportOptions
  ): Promise<{ success: boolean; downloadUrl?: string; error?: string }> {
    try {
      let content = `${quiz.title}\n`;
      content += `${'='.repeat(quiz.title.length)}\n\n`;
      
      if (quiz.description) {
        content += `${quiz.description}\n\n`;
      }

      content += `题目数量: ${questions.length}\n`;
      content += `总分: ${questions.reduce((sum, q) => sum + (q.points || 1), 0)}\n\n`;

      questions.forEach((question, index) => {
        content += `${index + 1}. ${question.text}\n`;
        
        if (question.type === 'mcq' && question.options) {
          question.options.forEach((option) => {
            const marker = options.includeAnswers && option.isCorrect ? '✓' : ' ';
            content += `   ${option.id.toUpperCase()}. [${marker}] ${option.text}\n`;
          });
        }

        if (question.type === 'true_false' && options.includeAnswers && question.correctAnswer) {
          content += `   正确答案: ${question.correctAnswer === 'true' ? '正确' : '错误'}\n`;
        }

        if (question.type === 'short_answer' && options.includeAnswers && question.correctAnswer) {
          content += `   参考答案: ${question.correctAnswer}\n`;
        }

        if (options.includeExplanations && question.explanation) {
          content += `   解释: ${question.explanation}\n`;
        }

        content += '\n';
      });

      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      const downloadUrl = URL.createObjectURL(blob);
      
      // 触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${quiz.title || 'quiz'}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return { success: true, downloadUrl };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '文本导出失败'
      };
    }
  }

  /**
   * 导出为JSON格式
   */
  private async exportToJSON(
    quiz: Quiz, 
    questions: QuizQuestion[], 
    options: ExportOptions
  ): Promise<{ success: boolean; downloadUrl?: string; error?: string }> {
    try {
      const exportData = {
        quiz: {
          ...quiz,
          questions: questions.map(q => ({
            ...q,
            // 根据选项决定是否包含答案
            ...(options.includeAnswers ? {} : {
              correctAnswer: undefined,
              options: q.options?.map(opt => ({
                ...opt,
                isCorrect: undefined
              }))
            }),
            // 根据选项决定是否包含解释
            ...(options.includeExplanations ? {} : {
              explanation: undefined
            })
          }))
        },
        exportedAt: new Date().toISOString(),
        exportOptions: options
      };

      const content = JSON.stringify(exportData, null, 2);
      const blob = new Blob([content], { type: 'application/json' });
      const downloadUrl = URL.createObjectURL(blob);
      
      // 触发下载
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `${quiz.title || 'quiz'}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      return { success: true, downloadUrl };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'JSON导出失败'
      };
    }
  }
} 