// 测试支付功能的简单脚本
// 用于验证 Creem.io 支付集成是否正常工作

const testPaymentData = {
  // 免费版测试数据
  free: {
    product_id: "free",
    product_name: "免费PDF转测验方案",
    credits: 5,
    interval: "one-time",
    amount: 0,
    currency: "USD",
    valid_months: 1,
    user_email: "<EMAIL>",
    user_uuid: "test-uuid-123"
  },
  
  // 专业版测试数据
  pro: {
    product_id: "pro",
    product_name: "专业测验生成器方案",
    credits: -1,
    interval: "month",
    amount: 1490,
    currency: "USD",
    valid_months: 1,
    user_email: "<EMAIL>",
    user_uuid: "test-uuid-123"
  }
};

async function testCreemCheckout(planType = 'free') {
  const testData = testPaymentData[planType];
  
  console.log(`测试 ${planType} 方案的支付流程...`);
  console.log('测试数据:', JSON.stringify(testData, null, 2));
  
  try {
    const response = await fetch('http://localhost:3000/api/creem-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    const result = await response.json();
    
    if (response.ok && result.code === 0) {
      console.log('✅ 支付会话创建成功!');
      console.log('支付链接:', result.data.checkout_url);
      console.log('订单号:', result.data.order_no);
      console.log('会话ID:', result.data.checkout_id);
    } else {
      console.log('❌ 支付会话创建失败:');
      console.log('错误信息:', result.message);
      console.log('完整响应:', result);
    }
  } catch (error) {
    console.log('❌ 请求失败:', error.message);
  }
}

// 验证环境变量配置
function checkEnvironmentVariables() {
  console.log('检查环境变量配置...');
  
  const requiredEnvVars = [
    'CREEM_API_KEY',
    'CREEM_API_URL',
    'CREEM_MODE',
    'NEXT_PUBLIC_WEB_URL',
    'NEXT_PUBLIC_PROJECT_NAME'
  ];
  
  const missing = [];
  
  requiredEnvVars.forEach(varName => {
    if (!process.env[varName]) {
      missing.push(varName);
    } else {
      console.log(`✅ ${varName}: ${process.env[varName]}`);
    }
  });
  
  if (missing.length > 0) {
    console.log('❌ 缺少以下环境变量:');
    missing.forEach(varName => console.log(`  - ${varName}`));
    return false;
  }
  
  return true;
}

// 主测试函数
async function runTests() {
  console.log('=== PDFtoQuiz 支付功能测试 ===\n');
  
  // 检查环境变量
  if (!checkEnvironmentVariables()) {
    console.log('\n请先配置必要的环境变量后再运行测试。');
    return;
  }
  
  console.log('\n=== 开始测试支付流程 ===\n');
  
  // 测试免费版
  await testCreemCheckout('free');
  
  console.log('\n---\n');
  
  // 测试专业版
  await testCreemCheckout('pro');
  
  console.log('\n=== 测试完成 ===');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testCreemCheckout,
  checkEnvironmentVariables,
  testPaymentData
};
