import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';
import { Quiz, QuizQuestion } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const { quiz, options } = await request.json();
    
    if (!quiz || !quiz.questions) {
      return NextResponse.json(
        { error: 'Invalid quiz data' },
        { status: 400 }
      );
    }

    // 生成HTML内容
    const htmlContent = generateQuizHTML(quiz, options);

    // 使用Puppeteer生成PDF
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      },
      printBackground: true
    });

    await browser.close();

    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(quiz.title || 'quiz')}.pdf"`,
      },
    });

  } catch (error) {
    console.error('PDF export error:', error);
    return NextResponse.json(
      { error: 'PDF export failed' },
      { status: 500 }
    );
  }
}

function generateQuizHTML(quiz: Quiz, options: any): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${quiz.title || 'Quiz Questions'}</title>
      <style>
        body {
          font-family: 'Arial', 'Helvetica', sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #e5e5e5;
          padding-bottom: 20px;
        }
        .title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #2563eb;
        }
        .description {
          font-size: 14px;
          color: #666;
          margin-bottom: 15px;
        }
        .info {
          font-size: 12px;
          color: #888;
        }
        .question {
          margin-bottom: 25px;
          padding: 15px;
          border: 1px solid #e5e5e5;
          border-radius: 8px;
          background-color: #fafafa;
        }
        .question-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 10px;
          color: #1f2937;
        }
        .option {
          margin: 8px 0;
          padding: 8px 12px;
          border-radius: 4px;
          background-color: white;
          border: 1px solid #e5e5e5;
        }
        .option.correct {
          background-color: #dcfce7;
          border-color: #16a34a;
          color: #15803d;
        }
        .answer {
          margin-top: 10px;
          padding: 10px;
          background-color: #dbeafe;
          border-radius: 4px;
          border-left: 4px solid #2563eb;
        }
        .explanation {
          margin-top: 10px;
          padding: 10px;
          background-color: #fef3c7;
          border-radius: 4px;
          border-left: 4px solid #f59e0b;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          font-size: 12px;
          color: #888;
          border-top: 1px solid #e5e5e5;
          padding-top: 20px;
        }
        @media print {
          body { margin: 0; }
          .question { break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">${quiz.title || 'Quiz Questions'}</div>
        ${quiz.description ? `<div class="description">${quiz.description}</div>` : ''}
        <div class="info">
          Questions: ${quiz.questions.length} | 
          Total Points: ${quiz.totalPoints || quiz.questions.length} | 
          Generated: ${new Date().toLocaleString('en-US')}
        </div>
      </div>

      <div class="questions">
        ${quiz.questions.map((question: QuizQuestion, index: number) => `
          <div class="question">
            <div class="question-title">${index + 1}. ${question.text}</div>
            
            ${question.type === 'mcq' && question.options ? `
              <div class="options">
                ${question.options.map((option) => `
                  <div class="option ${options.includeAnswers && option.isCorrect ? 'correct' : ''}">
                    ${option.id.toUpperCase()}. ${option.text}
                    ${options.includeAnswers && option.isCorrect ? ' ✓' : ''}
                  </div>
                `).join('')}
              </div>
            ` : ''}

            ${question.type === 'true_false' && options.includeAnswers && question.correctAnswer ? `
              <div class="answer">
                <strong>Correct Answer:</strong> ${question.correctAnswer === 'true' ? 'True' : 'False'}
              </div>
            ` : ''}

            ${question.type === 'short_answer' && options.includeAnswers && question.correctAnswer ? `
              <div class="answer">
                <strong>Reference Answer:</strong> ${question.correctAnswer}
              </div>
            ` : ''}

            ${options.includeExplanations && question.explanation ? `
              <div class="explanation">
                <strong>Explanation:</strong> ${question.explanation}
              </div>
            ` : ''}
          </div>
        `).join('')}
      </div>

      <div class="footer">
        <div>Generated by PDFtoQuiz | ${new Date().toLocaleString('en-US')}</div>
        ${quiz.sourceFile ? `<div>Source File: ${quiz.sourceFile.name}</div>` : ''}
      </div>
    </body>
    </html>
  `;
} 