import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
//import Hero from "@/components/blocks/hero";
//import HeroWithUpload from "@/components/blocks/hero/HeroWithUpload";
import HeroWithUploadEnhanced from "@/components/blocks/hero/HeroWithUploadEnhanced";
//import Generator from "@/components/generator";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import GhibliImageGallery from "@/components/ghibli-image-gallery";
import { getImage } from "@/models/image";
import { GhibliImage } from "@/components/ghibli-image-gallery";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);
  
  const initialImages = await getImage(1, 6);
  // const images: GhibliImage[] = [
  //   {
  //     id: "1",
  //     title: "Spring Festival Lanterns",
  //     img_url: "/imgs/showcases/3.jpg",
  //     dimensions: "1792×1024",
  //     author: {
  //       name: "Nina",
  //       //avatarUrl: "/avatars/nina.jpg",
  //     },
  //   },
  //   {
  //     id: "2",
  //     title: "Northern Landscape",
  //     img_url: "/imgs/showcases/4.jpg",
  //     dimensions: "1792×1024",
  //     author: {
  //       name: "Henry",
  //       //avatarUrl: "/avatars/henry.jpg",
  //     },
  //   },
  //   // ...更多图片
  // ];
  
  // 转换为 GhibliImage[]
  const ghibliImages: GhibliImage[] = initialImages.map(image => ({
    id: image.uuid,
    title: image.img_description.split(",")[0].replace("A high-resolution Studio Ghibli-style illustration of ", ""),
    img_url: image.img_url,
    dimensions: "1792×1024",
    author: {
      name: "User",
    },
  }));
  
  return (
    <>
      {/* {page.hero && <Hero hero={page.hero} />} */}
      {/* {page.hero && <HeroWithUpload hero={page.hero} />} */}
      {page.hero && <HeroWithUploadEnhanced hero={page.hero} />}
      {/* <Generator /> */}
      {/* <GhibliImageGallery images={ghibliImages} /> */}
      {/* {page.branding && <Branding section={page.branding} />} */}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
