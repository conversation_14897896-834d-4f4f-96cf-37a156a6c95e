import Empty from "@/components/blocks/empty";
import TableSlot from "@/components/console/slots/table";
import { Table as TableSlotType } from "@/types/slots/table";
import { getCreditsByUserUuid } from "@/models/credit";
import { getTranslations } from "next-intl/server";
import { getUserUuid } from "@/services/user";
import { QuotaAdapter } from "@/lib/quotaAdapter";
import QuotaDisplay from "@/components/quota/QuotaDisplay";
import QuotaUsageChart from "@/components/quota/QuotaUsageChart";
import moment from "moment";

export default async function MyQuotaPage() {
  const t = await getTranslations();
  const user_uuid = await getUserUuid();

  if (!user_uuid) {
    return <Empty message="未授权访问" />;
  }

  // 统一使用QuotaAdapter获取配额信息
  const quotaInfo = await QuotaAdapter.getQuotaDisplay(user_uuid);
  
  // 获取使用记录（转换为配额相关的显示）
  const creditHistory = await getCreditsByUserUuid(user_uuid, 1, 100);
  
  // 转换积分记录为配额使用记录
  const quotaHistory = creditHistory.map(record => ({
    ...record,
    characters_used: QuotaAdapter.creditsToCharacters(Math.abs(record.credits)),
    operation_display: getOperationDisplay(record.trans_type)
  }));

  const table: TableSlotType = {
    title: "配额使用记录",
    tip: {
      title: `剩余配额: ${quotaInfo.displayText}`,
      description: `计划: ${quotaInfo.plan} | 单次上传限制: ${
        quotaInfo.pdfLimitPerUpload ? `${quotaInfo.pdfLimitPerUpload}页` : '无限制'
      }`
    },
    toolbar: {
      items: [
        {
          title: "升级计划",
          url: "/#pricing",
          target: "_blank",
          icon: "RiVipCrownLine",
          variant: quotaInfo.plan === 'FREE' ? 'default' : 'outline'
        },
        {
          title: "查看订单",
          url: "/my-orders",
          icon: "RiOrderPlayLine",
          variant: "outline",
        },
      ],
    },
    columns: [
      {
        title: "操作类型",
        name: "operation_display",
      },
      {
        title: "字符使用量",
        name: "characters_used",
        callback: (record: any) => 
          `${(record.characters_used / 1000).toFixed(1)}K 字符`
      },
      {
        title: "积分变化",
        name: "credits",
        callback: (record: any) => 
          record.credits > 0 ? `+${record.credits}` : `${record.credits}`
      },
      {
        title: "时间",
        name: "created_at",
        callback: (record: any) => 
          moment(record.created_at).format("MM-DD HH:mm")
      },
    ],
    data: quotaHistory,
    empty_message: "暂无使用记录",
  };

  return (
    <div className="space-y-6">
      {/* 配额概览 */}
      <QuotaDisplay quota={quotaInfo} showUpgradeHint={true} />
      
      {/* 月度使用趋势图表 */}
      <QuotaUsageChart userUuid={user_uuid} />
      
      {/* 使用记录表格 */}
      <TableSlot {...table} />
    </div>
  );
}

function getOperationDisplay(transType: string): string {
  const typeMap = {
    'pdf_to_quiz': 'PDF转测验',
    'recharge': '账户充值',
    'manual_adjustment': '手动调整',
    'bonus': '系统奖励'
  };
  return typeMap[transType] || transType;
}
