'use client';

import { QuotaDisplayInfo } from '@/lib/quotaAdapter';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Zap, FileText, Upload, Calendar } from 'lucide-react';
import Link from 'next/link';

interface QuotaDisplayProps {
  quota: QuotaDisplayInfo;
  showUpgradeButton?: boolean;
  className?: string;
}

export default function QuotaDisplay({ 
  quota, 
  showUpgradeButton = true, 
  className = '' 
}: QuotaDisplayProps) {
  const usagePercentage = (quota.remainingCharacters / quota.totalCharacters) * 100;
  const isLowQuota = usagePercentage < 20;
  const isCriticalQuota = usagePercentage < 5;

  return (
    <Card className={`${className} ${isCriticalQuota ? 'border-red-200 bg-red-50' : isLowQuota ? 'border-yellow-200 bg-yellow-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Zap className="h-5 w-5" />
            字符配额
          </CardTitle>
          <Badge 
            variant={quota.plan === 'FREE' ? 'secondary' : 'default'}
            className="text-xs"
          >
            {quota.plan === 'FREE' ? '免费版' : 'Plus版'}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 配额使用进度 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">已使用</span>
            <span className="font-medium">{quota.displayText}</span>
          </div>
          <Progress 
            value={100 - usagePercentage} 
            className={`h-2 ${isCriticalQuota ? 'bg-red-100' : isLowQuota ? 'bg-yellow-100' : ''}`}
          />
          {isCriticalQuota && (
            <p className="text-xs text-red-600">⚠️ 配额即将用完，请考虑升级</p>
          )}
          {isLowQuota && !isCriticalQuota && (
            <p className="text-xs text-yellow-600">⚠️ 配额不足20%，建议升级</p>
          )}
        </div>

        {/* 限制信息 */}
        <div className="grid grid-cols-1 gap-3 text-sm">
          {quota.pdfLimitPerUpload && (
            <div className="flex items-center gap-2 text-gray-600">
              <FileText className="h-4 w-4" />
              <span>单次上传限制: {quota.pdfLimitPerUpload} 页</span>
            </div>
          )}
          
          {quota.maxUploadsPerMonth && (
            <div className="flex items-center gap-2 text-gray-600">
              <Upload className="h-4 w-4" />
              <span>月度上传限制: {quota.maxUploadsPerMonth} 次</span>
            </div>
          )}
          
          {quota.maxPagesPerMonth && (
            <div className="flex items-center gap-2 text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>月度页数限制: {quota.maxPagesPerMonth} 页</span>
            </div>
          )}
        </div>

        {/* 升级按钮 */}
        {showUpgradeButton && quota.plan === 'FREE' && (
          <div className="pt-2">
            <Link href="/#pricing">
              <Button className="w-full" size="sm">
                升级到 Plus 版
              </Button>
            </Link>
          </div>
        )}

        {/* 积分兼容显示 */}
        <div className="pt-2 border-t border-gray-100">
          <div className="flex justify-between text-xs text-gray-500">
            <span>剩余积分</span>
            <span>{quota.remainingCredits}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
