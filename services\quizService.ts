//import { openai } from '@ai-sdk/openai'; 
import { createOpenRouter } from "@openrouter/ai-sdk-provider";

import { generateObject } from 'ai';
import { z } from 'zod';
import { 
  Quiz, 
  QuizQuestion, 
  QuizGenerationOptions, 
  QuizGenerationResult,
  QuizOption 
} from '@/types/quiz';
import { PDFTextContent } from '@/types/pdf';

/**
 * AI测验生成服务类
 * 负责基于文档内容生成测验题目
 */
export class QuizService {
  private static instance: QuizService;

  public static getInstance(): QuizService {
    if (!QuizService.instance) {
      QuizService.instance = new QuizService();
    }
    return QuizService.instance;
  }

  /**
   * 基于PDF内容生成测验
   */
  public async generateQuiz(
    content: PDFTextContent,
    options: QuizGenerationOptions
  ): Promise<QuizGenerationResult> {
    const startTime = Date.now();

    try {
      // 预处理文本内容
      const processedText = this.preprocessText(content.text, options);
      
      // 生成测验题目
      const quiz = await this.generateQuizFromText(processedText, options);
      
      // 计算处理时间
      const processingTime = Date.now() - startTime;
      
      // 评估生成质量
      const confidence = this.assessQuizQuality(quiz, content);

      return {
        quiz,
        processingTime,
        extractedText: processedText,
        confidence,
        warnings: this.generateWarnings(quiz, content)
      };

    } catch (error) {
      console.error('Quiz generation error:', error);
      throw new Error(`Quiz generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 预处理文本内容
   */
  private preprocessText(text: string, options: QuizGenerationOptions): string {
    let processedText = text;

    // 清理文本
    processedText = processedText
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();

    // 限制文本长度以避免超出AI模型限制
    const maxLength = 8000; // 约2000个token
    if (processedText.length > maxLength) {
      // 智能截取：优先保留开头和重要段落
      const sections = processedText.split('\n\n');
      let result = '';
      let currentLength = 0;

      for (const section of sections) {
        if (currentLength + section.length <= maxLength) {
          result += section + '\n\n';
          currentLength += section.length + 2;
        } else {
          break;
        }
      }

      processedText = result.trim();
    }

    return processedText;
  }

  /**
   * 使用AI生成测验题目
   */
  private async generateQuizFromText(
    text: string,
    options: QuizGenerationOptions
  ): Promise<Quiz> {
    // 构建AI提示词
    const prompt = this.buildPrompt(text, options);

    // 定义返回数据的schema
    const quizSchema = z.object({
      title: z.string().describe('Quiz title'),
      description: z.string().optional().describe('Quiz description'),
      questions: z.array(z.object({
        text: z.string().describe('Question text'),
        type: z.enum(['mcq', 'true_false', 'short_answer']).describe('Question type'),
        options: z.array(z.object({
          text: z.string().describe('Option text'),
          isCorrect: z.boolean().describe('Whether this is the correct answer')
        })).optional().describe('Multiple choice options'),
        correctAnswer: z.string().optional().describe('Correct answer'),
        explanation: z.string().optional().describe('Answer explanation'),
        difficulty: z.enum(['easy', 'medium', 'hard']).describe('Question difficulty'),
        points: z.number().describe('Question points'),
        sourceText: z.string().optional().describe('Source text for the question')
      })).describe('List of quiz questions')
    });

    try {
      // 创建并使用 OpenRouter 客户端
      const openrouter = createOpenRouter({
        apiKey: process.env.OPENROUTER_API_KEY,
      });
      const { object } = await generateObject({
        model: openrouter('deepseek/deepseek-chat-v3-0324'), //deepseek/deepseek-r1-0528:free
        schema: quizSchema,
        prompt,
        temperature: 0.7,
      });

      // 转换为Quiz对象
      const quiz: Quiz = {
        id: `quiz_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: object.title,
        description: object.description,
        questions: object.questions.map((q, index) => ({
          id: `q_${index + 1}`,
          text: q.text,
          type: q.type,
          options: q.options?.map((opt, optIndex) => ({
            id: String.fromCharCode(97 + optIndex), // a, b, c, d
            text: opt.text,
            isCorrect: opt.isCorrect
          })),
          correctAnswer: q.correctAnswer,
          explanation: q.explanation,
          difficulty: q.difficulty,
          points: q.points,
          sourceText: q.sourceText
        })),
        totalQuestions: object.questions.length,
        totalPoints: object.questions.reduce((sum, q) => sum + q.points, 0),
        estimatedTime: Math.ceil(object.questions.length * 2), // 每题约2分钟
        difficulty: this.calculateOverallDifficulty(object.questions),
        tags: this.extractTags(text),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return quiz;

    } catch (error) {
      console.error('AI generation error:', error);
      throw new Error('AI quiz generation failed, please try again later');
    }
  }

  /**
   * 构建AI提示词
   */
  private buildPrompt(text: string, options: QuizGenerationOptions): string {
    const { questionCount, questionTypes, difficulty, language, includeExplanations } = options;

    const languageMap = {
      'zh': 'Chinese',
      'en': 'English'
    };

    const difficultyMap = {
      'easy': 'Easy',
      'medium': 'Medium',
      'hard': 'Hard',
      'mixed': 'Mixed difficulty'
    };

    return `
Please generate a quiz based on the following document content.

Document content:
${text}

Generation requirements:
- Number of questions: ${questionCount}
- Question types: ${questionTypes.join(', ')}
- Difficulty level: ${difficultyMap[difficulty]}
- Language: ${languageMap[language]}
- ${includeExplanations ? 'Include' : 'Do not include'} answer explanations

Specific requirements:
1. Questions should be based on the core content and important concepts of the document
2. Multiple choice questions should have 4 options with only one correct answer
3. True/false questions should be clearly stated to avoid ambiguity
4. Short answer questions should test understanding and application ability
5. Each question should be marked with difficulty level and points
6. If explanations are needed, provide clear answer explanations
7. Questions should cover different parts of the document, avoiding over-concentration

Please ensure the generated quiz is of high quality with clear questions and accurate answers.
    `.trim();
  }

  /**
   * 计算整体难度
   */
  private calculateOverallDifficulty(questions: any[]): 'easy' | 'medium' | 'hard' {
    const difficultyScores = { easy: 1, medium: 2, hard: 3 };
    const totalScore = questions.reduce((sum, q) => sum + difficultyScores[q.difficulty], 0);
    const averageScore = totalScore / questions.length;

    if (averageScore <= 1.3) return 'easy';
    if (averageScore <= 2.3) return 'medium';
    return 'hard';
  }

  /**
   * 从文本中提取标签
   */
  private extractTags(text: string): string[] {
    // 简单的关键词提取逻辑
    const commonWords = new Set(['的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过', 'the', 'is', 'are', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with']);
    
    const words = text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word));

    // 统计词频
    const wordCount = new Map<string, number>();
    words.forEach(word => {
      wordCount.set(word, (wordCount.get(word) || 0) + 1);
    });

    // 返回出现频率最高的前5个词作为标签
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([word]) => word);
  }

  /**
   * 评估测验质量
   */
  private assessQuizQuality(quiz: Quiz, content: PDFTextContent): number {
    let score = 0.5; // 基础分

    // 题目数量合理性
    if (quiz.questions.length >= 3 && quiz.questions.length <= 20) {
      score += 0.1;
    }

    // 题目类型多样性
    const uniqueTypes = new Set(quiz.questions.map(q => q.type));
    score += uniqueTypes.size * 0.1;

    // 内容覆盖度（简化评估）
    const textLength = content.text.length;
    if (textLength > 1000) {
      score += 0.1;
    }

    // 题目质量（基于文本长度）
    const avgQuestionLength = quiz.questions.reduce((sum, q) => sum + q.text.length, 0) / quiz.questions.length;
    if (avgQuestionLength > 20 && avgQuestionLength < 200) {
      score += 0.1;
    }

    return Math.min(score, 1.0);
  }

  /**
   * 生成警告信息
   */
  private generateWarnings(quiz: Quiz, content: PDFTextContent): string[] {
    const warnings: string[] = [];

    if (quiz.questions.length < 3) {
      warnings.push('Few questions generated, consider adding more content');
    }

    if (content.wordCount < 100) {
      warnings.push('Document content is limited, may affect question quality');
    }

    const mcqQuestions = quiz.questions.filter(q => q.type === 'mcq');
    const invalidMcq = mcqQuestions.filter(q => !q.options || q.options.length !== 4);
    if (invalidMcq.length > 0) {
      warnings.push('Some multiple choice questions have incorrect number of options');
    }

    return warnings;
  }

  /**
   * 验证测验数据
   */
  public validateQuiz(quiz: Quiz): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证基本信息
    if (!quiz.title || quiz.title.trim() === '') {
      errors.push('Quiz title cannot be empty');
    }

    if (quiz.questions.length === 0) {
      errors.push('Quiz must contain at least one question');
    }

    quiz.questions.forEach((question, index) => {
      if (!question.text || question.text.trim() === '') {
        errors.push(`Question ${index + 1} text cannot be empty`);
      }

      // 验证选择题
      if (question.type === 'mcq') {
        if (!question.options || question.options.length < 2) {
          errors.push(`Question ${index + 1} multiple choice must have at least 2 options`);
        } else {
          const correctOptions = question.options.filter(opt => opt.isCorrect);
          if (correctOptions.length !== 1) {
            errors.push(`Question ${index + 1} multiple choice must have exactly one correct answer`);
          }
        }
      }

      if (question.type === 'true_false' && !question.correctAnswer) {
        errors.push(`Question ${index + 1} true/false must specify correct answer`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
