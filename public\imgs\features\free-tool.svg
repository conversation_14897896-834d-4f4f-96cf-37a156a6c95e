<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <radialGradient id="freeGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.05" />
    </radialGradient>
  </defs>
  
  <rect width="300" height="200" fill="url(#freeGradient)" rx="12"/>
  
  <!-- 免费标签 -->
  <rect x="100" y="40" width="100" height="40" fill="#3b82f6" rx="20"/>
  <text x="150" y="65" text-anchor="middle" font-size="18" font-weight="bold" fill="white">FREE</text>
  
  <!-- 礼物盒图标 -->
  <rect x="130" y="90" width="40" height="30" fill="#1d4ed8" rx="4"/>
  <rect x="125" y="95" width="50" height="4" fill="#10b981"/>
  <path d="M140 95 Q150 85 160 95" stroke="#10b981" stroke-width="3" fill="none"/>
  
  <!-- 强调文字 -->
  <text x="150" y="140" text-anchor="middle" font-size="14" font-weight="bold" fill="#1f2937">Quick Google Login</text>
  <text x="150" y="160" text-anchor="middle" font-size="12" fill="#6b7280">Start Converting in Seconds</text>
  
  <!-- 装饰元素 -->
  <circle cx="80" cy="60" r="3" fill="#3b82f6" opacity="0.6"/>
  <circle cx="220" cy="80" r="2" fill="#1d4ed8" opacity="0.6"/>
  <circle cx="90" cy="140" r="2" fill="#10b981" opacity="0.6"/>
  <circle cx="210" cy="140" r="2" fill="#3b82f6" opacity="0.6"/>
</svg> 