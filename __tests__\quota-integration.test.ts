import { QuotaAdapter } from '@/lib/quotaAdapter';
import { QuotaService } from '@/services/quotaService';

// 模拟依赖
jest.mock('@/services/credit', () => ({
  getUserCredits: jest.fn(),
  decreaseCredits: jest.fn(),
  CreditsTransType: {
    PdfToQuiz: 'pdf_to_quiz'
  }
}));

jest.mock('@/models/user', () => ({
  findUserByUuid: jest.fn()
}));

import { getUserCredits, decreaseCredits } from '@/services/credit';
import { findUserByUuid } from '@/models/user';

const mockGetUserCredits = getUserCredits as jest.MockedFunction<typeof getUserCredits>;
const mockDecreaseCredits = decreaseCredits as jest.MockedFunction<typeof decreaseCredits>;
const mockFindUserByUuid = findUserByUuid as jest.MockedFunction<typeof findUserByUuid>;

describe('配额系统集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('免费用户配额检查', () => {
    beforeEach(() => {
      mockGetUserCredits.mockResolvedValue({
        left_credits: 100, // 100积分 = 100K字符
        is_recharged: false
      });
      
      mockFindUserByUuid.mockResolvedValue({
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: {
          left_credits: 100,
          is_recharged: false
        }
      } as any);
    });

    test('应该允许小文件处理', async () => {
      const result = await QuotaService.checkPdfProcessingQuota(
        'test-uuid',
        50000, // 50K字符
        5       // 5页
      );

      expect(result.hasQuota).toBe(true);
      expect(result.remainingCharacters).toBe(100000);
      expect(result.creditsNeeded).toBe(50); // 50K字符 = 50积分
    });

    test('应该拒绝超过页数限制的文件', async () => {
      const result = await QuotaService.checkPdfProcessingQuota(
        'test-uuid',
        50000, // 50K字符
        15     // 15页 > 5页限制
      );

      expect(result.hasQuota).toBe(false);
      expect(result.upgradeRequired).toBe(true);
      expect(result.message).toContain('Page limit exceeded');
    });

    test('应该拒绝超过字符配额的文件', async () => {
      const result = await QuotaService.checkPdfProcessingQuota(
        'test-uuid',
        150000, // 150K字符 > 100K剩余
        3       // 3页
      );

      expect(result.hasQuota).toBe(false);
      expect(result.upgradeRequired).toBe(true);
      expect(result.message).toBe('Insufficient quota');
    });
  });

  describe('Plus用户配额检查', () => {
    beforeEach(() => {
      mockGetUserCredits.mockResolvedValue({
        left_credits: 3000, // 3000积分 = 3M字符
        is_recharged: true
      });
      
      mockFindUserByUuid.mockResolvedValue({
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: {
          left_credits: 3000,
          is_recharged: true
        }
      } as any);
    });

    test('应该允许大文件处理', async () => {
      const result = await QuotaService.checkPdfProcessingQuota(
        'test-uuid',
        500000, // 500K字符
        20      // 20页
      );

      expect(result.hasQuota).toBe(true);
      expect(result.remainingCharacters).toBe(3000000);
      expect(result.creditsNeeded).toBe(500); // 500K字符 = 500积分
    });

    test('应该没有单次页数限制', async () => {
      const result = await QuotaService.checkPdfProcessingQuota(
        'test-uuid',
        100000, // 100K字符
        100     // 100页
      );

      expect(result.hasQuota).toBe(true);
      expect(result.message).toBe('Quota sufficient');
    });
  });

  describe('配额消费', () => {
    beforeEach(() => {
      mockGetUserCredits.mockResolvedValue({
        left_credits: 1000,
        is_recharged: false
      });
      
      mockDecreaseCredits.mockResolvedValue(undefined);
    });

    test('应该正确消费配额', async () => {
      const result = await QuotaService.consumePdfProcessingQuota(
        'test-uuid',
        50000, // 50K字符
        5      // 5页
      );

      expect(mockDecreaseCredits).toHaveBeenCalledWith({
        user_uuid: 'test-uuid',
        trans_type: 'pdf_to_quiz',
        credits: 50 // 50K字符 = 50积分
      });

      expect(result.success).toBe(true);
      expect(result.charactersUsed).toBe(50000);
      expect(result.creditsUsed).toBe(50);
    });

    test('应该处理消费失败的情况', async () => {
      mockDecreaseCredits.mockRejectedValue(new Error('Insufficient credits'));

      const result = await QuotaService.consumePdfProcessingQuota(
        'test-uuid',
        50000,
        5
      );

      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to consume quota');
    });
  });

  describe('配额使用统计', () => {
    test('应该正确计算使用统计', async () => {
      mockGetUserCredits.mockResolvedValue({
        left_credits: 150, // 剩余150积分
        is_recharged: false
      });
      
      mockFindUserByUuid.mockResolvedValue({
        id: 1,
        uuid: 'test-uuid',
        email: '<EMAIL>',
        credits: {
          left_credits: 150,
          is_recharged: false
        }
      } as any);

      const stats = await QuotaService.getQuotaUsageStats('test-uuid');

      expect(stats.usedCharacters).toBe(50000); // 200K总配额 - 150K剩余 = 50K已使用
      expect(stats.remainingCharacters).toBe(150000);
      expect(stats.usagePercentage).toBe(25); // 50K/200K = 25%
    });
  });
});
