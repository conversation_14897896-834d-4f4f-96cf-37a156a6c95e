import pdf from 'pdf-parse';
import { 
  PDFProcessingOptions, 
  PDFTextContent, 
  PDFValidationResult, 
  PDFProcessingResult,
  SupportedFileType,
  PDFPageContent,
  PDFMetadata,
  DocumentSection
} from '@/types/pdf';
import mammoth from 'mammoth';
import Tesseract from 'tesseract.js';

/**
 * PDF处理服务类
 * 负责PDF文件的解析、文本提取和内容处理
 */
export class PDFService {
  private static instance: PDFService;
  
  // 支持的文件类型配置
  private readonly supportedTypes: SupportedFileType[] = [
    {
      extension: '.pdf',
      mimeType: 'application/pdf',
      maxSize: 20 * 1024 * 1024, // 20MB
      processor: 'pdf-parse',
      description: 'PDF document'
    },
    {
      extension: '.docx',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      maxSize: 10 * 1024 * 1024, // 10MB
      processor: 'mammoth',
      description: 'Word document'
    },
    {
      extension: '.txt',
      mimeType: 'text/plain',
      maxSize: 5 * 1024 * 1024, // 5MB
      processor: 'text-reader',
      description: 'Plain text file'
    },
    {
      extension: '.jpg',
      mimeType: 'image/jpeg',
      maxSize: 10 * 1024 * 1024, // 10MB
      processor: 'image-ocr',
      description: 'JPEG image with OCR'
    },
    {
      extension: '.jpeg',
      mimeType: 'image/jpeg',
      maxSize: 10 * 1024 * 1024, // 10MB
      processor: 'image-ocr',
      description: 'JPEG image with OCR'
    },
    {
      extension: '.png',
      mimeType: 'image/png',
      maxSize: 10 * 1024 * 1024, // 10MB
      processor: 'image-ocr',
      description: 'PNG image with OCR'
    },
    {
      extension: '.bmp',
      mimeType: 'image/bmp',
      maxSize: 10 * 1024 * 1024, // 10MB
      processor: 'image-ocr',
      description: 'BMP image with OCR'
    },
    {
      extension: '.tiff',
      mimeType: 'image/tiff',
      maxSize: 10 * 1024 * 1024, // 10MB
      processor: 'image-ocr',
      description: 'TIFF image with OCR'
    }
  ];

  public static getInstance(): PDFService {
    if (!PDFService.instance) {
      PDFService.instance = new PDFService();
    }
    return PDFService.instance;
  }

  /**
   * 验证文件是否可以处理
   */
  public async validateFile(file: File): Promise<PDFValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查文件类型
    const supportedType = this.supportedTypes.find(
      type => type.mimeType === file.type || file.name.toLowerCase().endsWith(type.extension)
    );

    if (!supportedType) {
      errors.push(`Unsupported file type: ${file.type}`);
    }

    // 检查文件大小
    if (supportedType && file.size > supportedType.maxSize) {
      errors.push(`File size exceeds limit: ${this.formatFileSize(file.size)} > ${this.formatFileSize(supportedType.maxSize)}`);
    }

    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      errors.push('Filename cannot be empty');
    }

    // 估算处理时间
    let estimatedProcessingTime = 0;
    if (supportedType) {
      estimatedProcessingTime = Math.ceil(file.size / (1024 * 1024)) * 2; // 每MB约2秒
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified)
      },
      canProcess: errors.length === 0,
      estimatedProcessingTime
    };
  }

  /**
   * 处理PDF文件并提取文本内容
   */
  public async processPDF(
    file: File, 
    options: Partial<PDFProcessingOptions> = {}
  ): Promise<PDFProcessingResult> {
    const startTime = Date.now();

    try {
      // 验证文件
      const validation = await this.validateFile(file);
      if (!validation.canProcess) {
        return {
          success: false,
          error: validation.errors.join(', '),
          processingTime: Date.now() - startTime,
          quality: 'low'
        };
      }

      // 根据文件类型选择处理方法
      let content: PDFTextContent;
      
      if (file.type === 'application/pdf') {
        content = await this.extractPDFContent(file, options);
      } else if (file.type === 'text/plain') {
        content = await this.extractTextContent(file);
      } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        content = await this.extractDocxContent(file);
      } else if (file.type.startsWith('image/')) {
        content = await this.extractImageContent(file);
      } else {
        throw new Error(`File type ${file.type} is not supported for processing`);
      }

      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        content,
        processingTime,
        quality: this.assessContentQuality(content)
      };

    } catch (error) {
      console.error('File processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
        quality: 'low'
      };
    }
  }

  /**
   * 提取PDF内容
   */
  private async extractPDFContent(
    file: File, 
    options: Partial<PDFProcessingOptions>
  ): Promise<PDFTextContent> {
    const buffer = await file.arrayBuffer();
    const data = await pdf(Buffer.from(buffer));

    // 解析页面内容
    const pages: PDFPageContent[] = [];
    const textLines = data.text.split('\n');
    const linesPerPage = Math.ceil(textLines.length / data.numpages);

    for (let i = 0; i < data.numpages; i++) {
      const startLine = i * linesPerPage;
      const endLine = Math.min((i + 1) * linesPerPage, textLines.length);
      const pageText = textLines.slice(startLine, endLine).join('\n');
      
      pages.push({
        pageNumber: i + 1,
        text: pageText,
        wordCount: this.countWords(pageText),
        hasImages: false, // PDF-parse不直接提供图片信息
        confidence: 0.95 // PDF文本提取通常有较高置信度
      });
    }

    // 构建元数据
    const metadata: PDFMetadata = {
      title: data.info?.Title || file.name,
      author: data.info?.Author,
      subject: data.info?.Subject,
      creator: data.info?.Creator,
      producer: data.info?.Producer,
      creationDate: data.info?.CreationDate ? new Date(data.info.CreationDate) : undefined,
      modificationDate: data.info?.ModDate ? new Date(data.info.ModDate) : undefined,
      pageCount: data.numpages,
      fileSize: file.size,
      version: data.version,
      isEncrypted: false,
      hasFormFields: false
    };

    return {
      text: data.text,
      pages,
      metadata,
      processingTime: 0, // 将在上层计算
      wordCount: this.countWords(data.text),
      characterCount: data.text.length
    };
  }

  /**
   * 提取纯文本内容
   */
  private async extractTextContent(file: File): Promise<PDFTextContent> {
    const text = await file.text();
    
    const pages: PDFPageContent[] = [{
      pageNumber: 1,
      text,
      wordCount: this.countWords(text),
      hasImages: false,
      confidence: 1.0
    }];

    const metadata: PDFMetadata = {
      title: file.name,
      pageCount: 1,
      fileSize: file.size,
      isEncrypted: false,
      hasFormFields: false
    };

    return {
      text,
      pages,
      metadata,
      processingTime: 0,
      wordCount: this.countWords(text),
      characterCount: text.length
    };
  }

  /**
   * 提取DOCX内容
   */
  private async extractDocxContent(file: File): Promise<PDFTextContent> {
    const buffer = await file.arrayBuffer();
    const result = await mammoth.extractRawText({buffer: Buffer.from(buffer)});
    
    const text = result.value;
    const pages: PDFPageContent[] = [{
      pageNumber: 1,
      text: text,
      wordCount: this.countWords(text),
      hasImages: false,
      confidence: 0.9
    }];

    const metadata: PDFMetadata = {
      title: file.name,
      pageCount: 1,
      fileSize: file.size,
      isEncrypted: false,
      hasFormFields: false
    };

    return {
      text,
      pages,
      metadata,
      processingTime: 0,
      wordCount: this.countWords(text),
      characterCount: text.length
    };
  }

  /**
   * 提取图片内容（使用OCR）
   */
  private async extractImageContent(file: File): Promise<PDFTextContent> {
    try {
      // 使用Tesseract进行OCR识别
      const { data: { text, confidence } } = await Tesseract.recognize(file, 'eng+chi_sim', {
        logger: m => console.log('OCR进度:', m) // 可选：显示OCR进度
      });
      
      const pages: PDFPageContent[] = [{
        pageNumber: 1,
        text: text,
        wordCount: this.countWords(text),
        hasImages: true,
        confidence: confidence / 100 // Tesseract返回0-100，我们转换为0-1
      }];

      const metadata: PDFMetadata = {
        title: file.name,
        pageCount: 1,
        fileSize: file.size,
        isEncrypted: false,
        hasFormFields: false
      };

      return {
        text,
        pages,
        metadata,
        processingTime: 0,
        wordCount: this.countWords(text),
        characterCount: text.length
      };
    } catch (error) {
      console.error('OCR processing error:', error);
      throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 评估内容质量
   */
  private assessContentQuality(content: PDFTextContent): 'high' | 'medium' | 'low' {
    const { wordCount, characterCount } = content;
    
    // 基于字数和字符数评估质量
    if (wordCount < 50 || characterCount < 200) {
      return 'low';
    } else if (wordCount < 500 || characterCount < 2000) {
      return 'medium';
    } else {
      return 'high';
    }
  }

  /**
   * 统计单词数
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }

  /**
   * 清理和预处理文本
   */
  public cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // 合并多个空格
      .replace(/\n\s*\n/g, '\n\n') // 规范化段落分隔
      .trim();
  }

  /**
   * 将文本分割为段落
   */
  public splitIntoSections(text: string): DocumentSection[] {
    const sections: DocumentSection[] = [];
    const paragraphs = text.split(/\n\s*\n/);

    paragraphs.forEach((paragraph, index) => {
      if (paragraph.trim()) {
        sections.push({
          content: paragraph.trim(),
          pageNumbers: [1], // 简化处理，实际应该根据页面分布计算
          type: 'paragraph',
          wordCount: this.countWords(paragraph)
        });
      }
    });

    return sections;
  }
}
