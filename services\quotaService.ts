import { QuotaAdapter, QuotaDisplayInfo, QuotaCheckResult, ConsumeQuotaResult } from '@/lib/quotaAdapter';
import { getUserCredits, CreditsTransType } from './credit';
import { findUserByUuid } from '@/models/user';

/**
 * 配额服务类 - 提供配额相关的业务逻辑
 * 基于QuotaAdapter适配器实现具体的业务功能
 */
export class QuotaService {
  /**
   * 检查PDF处理配额
   */
  static async checkPdfProcessingQuota(
    userUuid: string,
    estimatedCharacters: number,
    pagesCount: number
  ): Promise<QuotaCheckResult> {
    return QuotaAdapter.checkQuota(userUuid, estimatedCharacters, pagesCount);
  }

  /**
   * 消费PDF处理配额
   */
  static async consumePdfProcessingQuota(
    userUuid: string,
    actualCharacters: number,
    pagesProcessed: number
  ): Promise<ConsumeQuotaResult> {
    return QuotaAdapter.consumeQuota(
      userUuid, 
      actualCharacters, 
      CreditsTransType.PdfToQuiz  // 需要在CreditsTransType中新增此类型
    );
  }

  /**
   * 获取用户配额信息（前端显示）
   */
  static async getUserQuotaInfo(userUuid: string): Promise<QuotaDisplayInfo> {
    const userCredits = await getUserCredits(userUuid);
    const user = await findUserByUuid(userUuid);
    
    return QuotaAdapter.getQuotaDisplay({
      ...user,
      credits: userCredits
    } as any);
  }

  /**
   * 估算PDF字符数（简化算法）
   * @param pagesCount PDF页数
   * @param fileSize 文件大小（字节）
   * @returns 估算的字符数
   */
  static estimatePdfCharacters(pagesCount: number, fileSize?: number): number {
    // 简化算法：每页约1000字符
    // 后续可以根据实际情况优化算法
    return pagesCount * 1000;
  }

  /**
   * 检查用户是否需要升级
   */
  static async checkUpgradeRequired(userUuid: string, charactersNeeded: number): Promise<boolean> {
    const quotaCheck = await this.checkPdfProcessingQuota(userUuid, charactersNeeded, 1);
    return quotaCheck.upgradeRequired;
  }

  /**
   * 获取配额使用统计信息
   */
  static async getQuotaUsageStats(userUuid: string): Promise<{
    usedCharacters: number;
    remainingCharacters: number;
    usagePercentage: number;
  }> {
    const quotaInfo = await this.getUserQuotaInfo(userUuid);
    const usedCharacters = quotaInfo.totalCharacters - quotaInfo.remainingCharacters;
    const usagePercentage = (usedCharacters / quotaInfo.totalCharacters) * 100;

    return {
      usedCharacters,
      remainingCharacters: quotaInfo.remainingCharacters,
      usagePercentage: Math.round(usagePercentage * 100) / 100, // 保留两位小数
    };
  }
}
