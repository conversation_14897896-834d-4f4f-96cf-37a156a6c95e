'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { FileUploader } from '@/components/upload/FileUploader';
import QuizResultsEnhanced from '@/components/quiz/QuizResultsEnhanced';
import { ExportService } from '@/services/exportService';
import { Quiz, QuizAttempt } from '@/types/quiz';
import { toast } from 'sonner';
import QuizTaking from '@/components/quiz/QuizTaking';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ShareService } from '@/services/shareService';
import { FileText } from 'lucide-react';

interface HeroWithUploadEnhancedProps {
  hero: any; // 根据实际的hero类型定义
}

export default function HeroWithUploadEnhanced({ hero }: HeroWithUploadEnhancedProps) {
  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null);
  const [quizMode, setQuizMode] = useState<'taking' | 'results' | 'management'>('taking');
  const [quizAttempt, setQuizAttempt] = useState<QuizAttempt | undefined>(undefined);

  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  const handleFileUpload = (file: File) => {
    console.log('File uploaded:', file.name);
  };

  const handleQuizGenerated = (quiz: Quiz) => {
    console.log('Quiz generated:', quiz);
    setGeneratedQuiz(quiz);
    setQuizMode('taking');
  };

  const handleQuizComplete = (attempt: QuizAttempt) => {
    setQuizAttempt(attempt);
    setQuizMode('results');
    toast.success(`Quiz completed! Score: ${attempt.percentage}%`);
  };

  const handleRetakeQuiz = () => {
    setQuizAttempt(undefined);
    setQuizMode('taking');
  };

  const handleViewManagement = () => {
    setQuizMode('management');
  };

  const handleBackToUpload = () => {
    setQuizMode('taking');
    setGeneratedQuiz(null);
  };

  const handleExport = async (format: string, selectedQuestions?: string[]) => {
    if (!generatedQuiz) {
      toast.error('No quiz available for export');
      return;
    }

    const exportService = ExportService.getInstance();
    const result = await exportService.exportQuiz(generatedQuiz, {
      format: format as 'pdf' | 'docx' | 'txt' | 'json',
      selectedQuestions: selectedQuestions || [],
      includeAnswers: true,
      includeExplanations: true,
      template: 'standard'
    });

    if (result.success) {
      toast.success(`Quiz successfully exported as ${format.toUpperCase()} format`);
    } else {
      toast.error(result.error || 'Export failed');
    }
  };

  const handleShare = async () => {
    if (!generatedQuiz) {
      toast.error('No quiz available for sharing');
      return;
    }
    
    try {
      // const shareService = ShareService.getInstance();
      // const result = await shareService.generateShareLink(generatedQuiz, {
      //   platform: 'link',
      //   includeAnswers: false // 默认不包含答案
      // });

      // if (result.success && result.shareUrl) {
      //   await navigator.clipboard.writeText(result.shareUrl);
      //   toast.success('Share link copied to clipboard!');
      // } else {
      //   toast.error(result.error || 'Failed to generate share link');
      // }
      const shareText = `🎯 Quiz: "${generatedQuiz.title}"\n📝 ${generatedQuiz.totalQuestions} questions\n⏱️ Est. time: ${generatedQuiz.estimatedTime || 10} minutes\n\nGenerated with PDFtoQuiz - Convert your PDFs to interactive quizzes!`;
    
      await navigator.clipboard.writeText(shareText);
      toast.success('Quiz details copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleEdit = (questionId: string) => {
    console.log('Edit question:', questionId);
    toast.info('Question editing feature coming soon');
    // TODO: 实现编辑功能
  };

  return (
    <section className="py-24">
      <div className="container mx-auto px-4">
        {!generatedQuiz ? (
          // 上传界面
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900">
                {texts ? (
                  <>
                    {texts[0]}
                    <span className="text-blue-600">{highlightText}</span>
                    {texts[1]}
                  </>
                ) : (
                  hero.title
                )}
              </h1>
              {hero.description && (
                <p 
                  className="text-xl text-gray-600 max-w-3xl mx-auto"
                  dangerouslySetInnerHTML={{ __html: hero.description }}
                />
              )}
            </div>

            <div className="max-w-2xl mx-auto">
              <FileUploader
                onFileUpload={handleFileUpload}
                onQuizGenerated={handleQuizGenerated}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 模式切换按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <Button
                  variant={quizMode === 'taking' ? 'default' : 'outline'}
                  onClick={() => setQuizMode('taking')}
                >
                  Taking Mode
                </Button>
                <Button
                  variant={quizMode === 'results' ? 'default' : 'outline'}
                  onClick={() => setQuizMode('results')}
                  disabled={!quizAttempt}
                >
                  View Results
                </Button>
                <Button
                  variant={quizMode === 'management' ? 'default' : 'outline'}
                  onClick={() => setQuizMode('management')}
                >
                  Manage Questions
                </Button>
              </div>
              <Button variant="outline" onClick={handleBackToUpload}>
                Back to Upload
              </Button>
            </div>

            {/* 统一使用一个组件，通过mode属性控制显示 */}
            <QuizResultsEnhanced
              quiz={generatedQuiz}
              attempt={quizAttempt}
              mode={quizMode}
              onComplete={handleQuizComplete}
              onRetake={handleRetakeQuiz}
              onEdit={handleEdit}
              onExport={handleExport}
              onShare={handleShare}
            />
          </div>
        )}
      </div>
    </section>
  );
} 