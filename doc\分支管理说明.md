PDFtoQuiz 项目分支管理说明

## 🌳 分支结构概览

```
pdftoquiz (repository)
├── main                    # 主分支 - PDFtoQuiz 专用项目
│   ├── 字符数限制模式 (1M字符/月)
│   ├── PDF转测验特定功能
│   ├── 可能移除积分系统
│   └── 针对教育场景优化
│
└── template-credits        # 模板分支 - 通用积分充值模板
    ├── 积分充值系统
    ├── 月付套餐增加积分
    ├── 其他AI工具可复用
    └── 保持通用性设计
```

## 📋 分支用途说明

### main 分支 (PDFtoQuiz 专用)

**用途**: PDFtoQuiz 项目的主要开发分支，专注于 PDF 转测验的特定业务需求。

**特征**:
- 🎯 **业务模式**: 基于字符数限制 (如 1M字符/月)
- 📄 **核心功能**: PDF 解析、AI 生成测验、OCR 支持
- 💰 **计费方式**: 可能不使用积分，直接按字符数/功能计费
- 🎓 **目标用户**: 教育工作者、学生、培训机构
- 🔧 **技术栈**: Next.js + Creem.io + DeepSeek V3

**主要功能模块**:
```
app/api/pdf/convert-to-quiz/    # PDF转测验API
components/quiz/                # 测验相关组件
services/pdf-parser.ts          # PDF解析服务
lib/ai-quiz-generator.ts        # AI测验生成
config/character-limits.ts      # 字符数限制配置
```

**定价策略** (参考 `doc/定价策略.md`):
- Free: 0.2M 字符/月 (5页上限)
- Basic: $12/月, 5M 字符/月 (50页/月)
- Pro: $25/月, 15M 字符/月 (200页/月)
- Elite: $45/月, 30M 字符/月 (无限制)

### template-credits 分支 (通用积分模板)

**用途**: 可复用的积分充值系统模板，适用于各种 AI 工具项目。

**特征**:
- 🔄 **业务模式**: 月付套餐 → 增加积分 → 消费积分使用服务
- 🛠️ **通用设计**: 移除 PDF 特定逻辑，保持业务无关性
- 💳 **支付集成**: 完整的 Creem.io 支付系统
- 📊 **用户管理**: 积分余额、消费记录、订单历史
- 🎨 **UI组件**: 通用的定价页面、积分显示、充值流程

**核心模块**:
```
app/api/credits/                # 积分管理API
components/credits/             # 积分相关组件
services/credits.ts             # 积分服务层
config/pricing-template.ts     # 通用定价配置
doc/模板使用指南.md            # 使用文档
```

**模板定价示例**:
- Free: $0, 100积分赠送
- Basic: $9/月, 1000积分/月
- Pro: $25/月, 5000积分/月  
- Elite: $45/月, 15000积分/月

## 🚀 Git 操作指南

### 创建和设置分支

```bash
# 1. 创建模板分支
git checkout main
git checkout -b template-credits
git push -u origin template-credits

# 2. 在模板分支中进行通用化改造
# 编辑代码...

# 3. 提交模板代码
git add .
git commit -m "feat: 创建积分充值模板

- 添加通用积分系统
- 移除PDF特定逻辑  
- 创建可复用组件
- 添加模板使用文档"

git push origin template-credits

# 4. 回到主分支继续 PDFtoQuiz 开发
git checkout main

# 5. 未来同步改进到模板分支
git checkout template-credits
git merge main  # 合并主分支的通用改进
git push origin template-credits

# 6. 基于模板创建新项目
git clone -b template-credits https://github.com/your-repo.git new-ai-tool
```

### 分支切换和开发

```bash
# 开发 PDFtoQuiz 功能
git checkout main
# 进行PDF相关功能开发...

# 维护积分模板
git checkout template-credits  
# 优化积分系统通用性...

# 查看当前分支
git branch
git status
```

## 🔄 分支同步策略

### 从 main 到 template-credits 的同步

```bash
# 当 main 分支有通用改进时（如UI优化、支付系统改进）
git checkout template-credits
git merge main

# 解决冲突原则：
# 1. 保留通用功能改进
# 2. 移除PDF特定逻辑
# 3. 保持模板的可复用性
```

### 冲突解决示例

```bash
# 如果遇到冲突
git merge main
# 手动编辑冲突文件
# 移除 PDF 特定代码，保留通用改进
git add .
git commit -m "merge: 同步main分支通用改进，保持模板通用性"
git push origin template-credits
```

## 📁 分支文件差异对比

### main 分支特有文件
```
app/api/pdf/
├── convert-to-quiz/route.ts    # PDF转测验API
└── parse/route.ts              # PDF解析API

components/quiz/
├── quiz-generator.tsx          # 测验生成器
├── quiz-preview.tsx            # 测验预览
└── question-editor.tsx         # 题目编辑器

services/
├── pdf-parser.ts               # PDF解析服务
└── ai-quiz-generator.ts        # AI生成测验

config/
└── character-limits.ts         # 字符数限制配置
```

### template-credits 分支特有文件
```
app/api/credits/
├── add/route.ts                # 增加积分API
├── deduct/route.ts             # 扣除积分API
└── balance/route.ts            # 查询余额API

components/credits/
├── credit-balance.tsx          # 积分余额显示
├── credit-history.tsx          # 积分历史记录
└── recharge-modal.tsx          # 充值弹窗

services/
└── credits.ts                  # 积分管理服务

config/
├── pricing-template.ts         # 通用定价模板
└── credit-rates.ts             # 积分费率配置

doc/
└── 模板使用指南.md             # 模板使用文档
```

### 共同文件
```
app/api/creem-*/                # Creem.io支付API
components/blocks/pricing/       # 定价页面组件
services/creem.ts               # 支付服务
types/order.d.ts                # 订单类型定义
```

## 🎯 使用场景

### 继续开发 PDFtoQuiz (main 分支)
```bash
git checkout main

# 可以进行的开发：
# 1. 优化PDF解析算法
# 2. 添加更多题目类型
# 3. 改进AI生成质量
# 4. 添加OCR功能
# 5. 移除积分系统，改用字符数直接计费
```

### 创建新的AI工具项目 (template-credits 分支)
```bash
# 克隆模板开始新项目
git clone -b template-credits https://github.com/your-username/pdftoquiz.git ai-image-generator
cd ai-image-generator

# 自定义配置：
# 1. 修改 .env.local 中的项目名称
# 2. 调整 config/pricing-template.ts 中的定价
# 3. 修改积分消费逻辑 (如：1积分=1张图片)
# 4. 更新UI文案和品牌信息
```

## 📋 分支维护清单

### main 分支维护
- [ ] 专注PDFtoQuiz功能开发
- [ ] 可以移除积分系统相关代码
- [ ] 优化字符数计费逻辑
- [ ] 添加PDF特有功能
- [ ] 保持代码质量和性能

### template-credits 分支维护  
- [ ] 保持积分系统的通用性
- [ ] 移除所有业务特定逻辑
- [ ] 维护完整的支付集成
- [ ] 更新模板使用文档
- [ ] 定期同步main分支的通用改进

### 同步维护
- [ ] 定期将main分支的通用改进合并到template-credits
- [ ] 解决合并冲突时优先保持模板通用性
- [ ] 更新两个分支的文档
- [ ] 测试两个分支的功能完整性

## 📞 分支使用建议

1. **开发PDFtoQuiz新功能** → 切换到 `main` 分支
2. **创建新的AI工具项目** → 基于 `template-credits` 分支
3. **优化支付系统** → 在 `main` 分支开发，然后合并到 `template-credits`
4. **修复通用bug** → 在 `main` 分支修复，然后合并到 `template-credits`

---

**维护者**: PDFtoQuiz 开发团队  
**最后更新**: 2024年12月  
**版本**: v1.0
```

这个文档详细说明了：

1. **分支结构** - 清晰的分支用途和特征对比
2. **Git 操作** - 完整的分支创建、切换、同步命令
3. **文件差异** - 两个分支的具体文件结构对比
4. **使用场景** - 什么时候使用哪个分支
5. **维护策略** - 如何保持两个分支的同步和独立性

这样你就有了完整的分支管理文档，未来其他开发者也能清楚地理解项目的分支策略。


# 1. 创建模板分支
git checkout main
git checkout -b template-credits
git push -u origin template-credits

# 2. 在模板分支中进行通用化改造
# 编辑代码...

# 3. 提交模板代码
git add .
git commit -m "feat: 创建积分充值模板"
git push origin template-credits

# 4. 回到主分支继续 PDFtoQuiz 开发
git checkout main

# 5. 未来同步改进到模板分支
git checkout template-credits
git merge main  # 合并主分支的通用改进
git push origin template-credits

# 6. 基于模板创建新项目
git clone -b template-credits https://github.com/your-repo.git new-ai-tool
