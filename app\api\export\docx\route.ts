import { NextRequest, NextResponse } from 'next/server';
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx';
import { Quiz, QuizQuestion } from '@/types/quiz';

export async function POST(request: NextRequest) {
  try {
    const { quiz, options } = await request.json();
    
    if (!quiz || !quiz.questions) {
      return NextResponse.json(
        { error: 'Invalid quiz data' },
        { status: 400 }
      );
    }

    // 创建文档段落
    const paragraphs = [];

    // 标题
    paragraphs.push(
      new Paragraph({
        text: quiz.title || 'Quiz Questions',
        heading: HeadingLevel.TITLE,
      })
    );

    // 描述
    if (quiz.description) {
      paragraphs.push(
        new Paragraph({
          text: quiz.description,
        })
      );
    }

    // 基本信息
    paragraphs.push(
      new Paragraph({
        text: `Questions: ${quiz.questions.length}`,
      }),
      new Paragraph({
        text: `Total Points: ${quiz.totalPoints || quiz.questions.length}`,
      }),
      new Paragraph({ text: '' }) // 空行
    );

    // 添加题目
    quiz.questions.forEach((question: QuizQuestion, index: number) => {
      // 题目标题
      paragraphs.push(
        new Paragraph({
          text: `${index + 1}. ${question.text}`,
          heading: HeadingLevel.HEADING_2,
        })
      );

      // 选择题选项
      if (question.type === 'mcq' && question.options) {
        question.options.forEach((option) => {
          const marker = options.includeAnswers && option.isCorrect ? '✓' : ' ';
          paragraphs.push(
            new Paragraph({
              text: `   ${option.id.toUpperCase()}. [${marker}] ${option.text}`,
            })
          );
        });
      }

      // 判断题答案
      if (question.type === 'true_false' && options.includeAnswers && question.correctAnswer) {
        paragraphs.push(
          new Paragraph({
            text: `   Correct Answer: ${question.correctAnswer === 'true' ? 'True' : 'False'}`,
          })
        );
      }

      // 简答题答案
      if (question.type === 'short_answer' && options.includeAnswers && question.correctAnswer) {
        paragraphs.push(
          new Paragraph({
            text: `   Reference Answer: ${question.correctAnswer}`,
          })
        );
      }

      // 解释
      if (options.includeExplanations && question.explanation) {
        paragraphs.push(
          new Paragraph({
            text: `   Explanation: ${question.explanation}`,
          })
        );
      }

      paragraphs.push(new Paragraph({ text: '' })); // 题目间空行
    });

    // 创建文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: paragraphs,
        },
      ],
    });

    // 生成Word文档
    const buffer = await Packer.toBuffer(doc);

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename="${quiz.title || 'quiz'}.docx"`,
      },
    });

  } catch (error) {
    console.error('Word export error:', error);
    return NextResponse.json(
      { error: 'Word export failed' },
      { status: 500 }
    );
  }
} 