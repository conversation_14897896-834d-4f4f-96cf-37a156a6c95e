# 配额系统实现方案设计

## 📋 项目概述

**目标**：设计积分系统改造到字符配额系统的方案，最大程度复用现有代码架构。

**设计原则**：
- 最小化现有代码改动
- 保持向后兼容性
- 复用现有数据库结构
- 渐进式迁移策略

---

## 🔍 现状分析

### 1.1 现有积分系统核心组件

#### 数据层
```sql
-- 现有表结构
CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at timestamptz,
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,                -- 核心字段：积分数
    order_no VARCHAR(255),
    expired_at timestamptz               -- 过期时间
);

CREATE TABLE orders (
    -- ... 其他字段
    credits INT NOT NULL,                -- 订单包含的积分数
    -- ...
);
```

#### 服务层
- `services/credit.ts`: 核心积分服务
  - `getUserCredits()`: 获取用户积分
  - `decreaseCredits()`: 扣减积分
  - `increaseCredits()`: 增加积分
  - `updateCreditForOrder()`: 订单支付后增加积分

#### 类型定义
- `types/credit.d.ts`: Credit接口
- `types/user.d.ts`: UserCredits接口

#### API接口
- `/api/get-user-info`: 获取用户信息和积分
- `/api/ping`: 消费积分的示例API
- `/api/gen-image`: 图片生成消费积分

### 1.2 可复用组件评估

**✅ 高度可复用**：
- 数据库表结构（credits, orders）
- 基础CRUD操作（models/credit.ts）
- 支付流程（Creem.io集成）
- 前端组件（my-credits页面，pricing组件）

**⚠️ 需要适配**：
- 积分计算逻辑
- 前端显示逻辑
- 配额检查机制

**❌ 需要新增**：
- 字符数统计功能
- 页数限制检查
- 月度配额重置机制

### 1.3 月度配额统计与重置机制

```sql
-- 新增表结构
CREATE TABLE monthly_quota_usage (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    month DATE NOT NULL,
    characters_used INT NOT NULL,
    pages_processed INT NOT NULL,
    uploads_count INT NOT NULL
);
```

---

## 🎯 最小改动迁移方案

### 2.1 适配器模式设计（推荐方案）

#### 核心思路
保留现有credits系统作为底层存储，在上层添加字符配额转换层。

```typescript
// lib/quotaAdapter.ts
export class QuotaAdapter {
  // 配额转换规则
  private static readonly PLAN_CONFIGS = {
    FREE: {
      monthlyCredits: 200,              // 0.2M字符 = 200积分
      charactersPerCredit: 1000,        // 1积分 = 1000字符
      pdfLimitPerUpload: 10,            // 单次上传页数限制
      maxUploadsPerMonth: 5,            // 月度上传次数限制
    },
    PLUS: {
      monthlyCredits: 5000,             // 5M字符 = 5000积分
      charactersPerCredit: 1000,        // 1积分 = 1000字符
      pdfLimitPerUpload: null,          // 无单次页数限制
      maxPagesPerMonth: 50,             // 月度总页数限制
    }
  };

  /**
   * 将字符数转换为积分数
   */
  static charactersToCredits(characters: number): number {
    return Math.ceil(characters / 1000);
  }

  /**
   * 将积分数转换为字符数
   */
  static creditsToCharacters(credits: number): number {
    return credits * 1000;
  }

  /**
   * 获取用户配额显示信息
   */
  static getQuotaDisplay(user: User): QuotaDisplayInfo {
    const plan = this.getUserPlan(user);
    const config = this.PLAN_CONFIGS[plan];
    const remainingCredits = user.credits?.left_credits || 0;
    const remainingCharacters = this.creditsToCharacters(remainingCredits);
    const totalCharacters = this.creditsToCharacters(config.monthlyCredits);

    return {
      plan,
      remainingCharacters,
      totalCharacters,
      remainingCredits,
      displayText: `${(remainingCharacters / 1000).toFixed(1)}K / ${(totalCharacters / 1000).toFixed(1)}K characters`,
      pdfLimitPerUpload: config.pdfLimitPerUpload,
      maxUploadsPerMonth: config.maxUploadsPerMonth,
      maxPagesPerMonth: config.maxPagesPerMonth,
    };
  }

  /**
   * 检查是否有足够配额
   */
  static async checkQuota(
    userUuid: string, 
    charactersNeeded: number,
    pagesCount?: number
  ): Promise<QuotaCheckResult> {
    const userCredits = await getUserCredits(userUuid);
    const user = await findUserByUuid(userUuid);
    const plan = this.getUserPlan(user);
    const config = this.PLAN_CONFIGS[plan];
    
    const creditsNeeded = this.charactersToCredits(charactersNeeded);
    const hasEnoughCredits = userCredits.left_credits >= creditsNeeded;
    
    // 检查页数限制
    let pageCheckPassed = true;
    if (config.pdfLimitPerUpload && pagesCount) {
      pageCheckPassed = pagesCount <= config.pdfLimitPerUpload;
    }
    
    return {
      hasQuota: hasEnoughCredits && pageCheckPassed,
      remainingCharacters: this.creditsToCharacters(userCredits.left_credits),
      remainingCredits: userCredits.left_credits,
      creditsNeeded,
      message: hasEnoughCredits ? 
        (pageCheckPassed ? 'Quota sufficient' : `Page limit exceeded (max ${config.pdfLimitPerUpload} pages)`) :
        'Insufficient quota',
      upgradeRequired: !hasEnoughCredits || !pageCheckPassed
    };
  }

  /**
   * 消费配额（字符数）
   */
  static async consumeQuota(
    userUuid: string,
    charactersUsed: number,
    operationType: string = 'pdf_to_quiz'
  ): Promise<ConsumeQuotaResult> {
    const creditsToConsume = this.charactersToCredits(charactersUsed);
    
    try {
      // 复用现有积分扣减逻辑
      await decreaseCredits({
        user_uuid: userUuid,
        trans_type: operationType as CreditsTransType,
        credits: creditsToConsume,
      });
      
      const remainingCredits = await getUserCredits(userUuid);
      
      return {
        success: true,
        remainingCharacters: this.creditsToCharacters(remainingCredits.left_credits),
        remainingCredits: remainingCredits.left_credits,
        charactersUsed,
        creditsUsed: creditsToConsume,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to consume quota: ${error}`,
        remainingCharacters: 0,
        remainingCredits: 0,
      };
    }
  }

  private static getUserPlan(user: User): 'FREE' | 'PLUS' {
    // 根据用户订阅状态判断计划
    return user.credits?.is_recharged ? 'PLUS' : 'FREE';
  }
}
```

### 2.2 类型定义扩展

```typescript
// types/quota.d.ts
export interface QuotaDisplayInfo {
  plan: 'FREE' | 'PLUS';
  remainingCharacters: number;
  totalCharacters: number;
  remainingCredits: number;
  displayText: string;
  pdfLimitPerUpload: number | null;
  maxUploadsPerMonth: number | null;
  maxPagesPerMonth: number | null;
}

export interface QuotaCheckResult {
  hasQuota: boolean;
  remainingCharacters: number;
  remainingCredits: number;
  creditsNeeded: number;
  message: string;
  upgradeRequired: boolean;
}

export interface ConsumeQuotaResult {
  success: boolean;
  remainingCharacters: number;
  remainingCredits: number;
  charactersUsed?: number;
  creditsUsed?: number;
  message?: string;
}
```

### 2.3 服务层适配

```typescript
// services/quotaService.ts
import { QuotaAdapter } from '@/lib/quotaAdapter';
import { getUserCredits, CreditsTransType } from './credit';

export class QuotaService {
  /**
   * 检查PDF处理配额
   */
  static async checkPdfProcessingQuota(
    userUuid: string,
    estimatedCharacters: number,
    pagesCount: number
  ): Promise<QuotaCheckResult> {
    return QuotaAdapter.checkQuota(userUuid, estimatedCharacters, pagesCount);
  }

  /**
   * 消费PDF处理配额
   */
  static async consumePdfProcessingQuota(
    userUuid: string,
    actualCharacters: number,
    pagesProcessed: number
  ): Promise<ConsumeQuotaResult> {
    return QuotaAdapter.consumeQuota(
      userUuid, 
      actualCharacters, 
      CreditsTransType.PdfToQuiz  // 新增交易类型
    );
  }

  /**
   * 获取用户配额信息（前端显示）
   */
  static async getUserQuotaInfo(userUuid: string): Promise<QuotaDisplayInfo> {
    const userCredits = await getUserCredits(userUuid);
    const user = await findUserByUuid(userUuid);
    
    return QuotaAdapter.getQuotaDisplay({
      ...user,
      credits: userCredits
    } as User);
  }
}
```

### 2.4 并发与事务控制

- 使用事务确保消费配额的原子性
- 使用乐观锁避免并发冲突
- 使用分布式锁控制重置配额的并发执行

---

## 🔧 具体实现策略

### 3.1 数据库层面（无需修改）

**保持现有表结构不变**：
- `credits`表继续存储积分交易记录
- `orders`表继续存储订单和积分信息
- 通过转换逻辑实现字符配额功能

**新增数据表**：
- `monthly_quota_usage`表用于统计月度配额使用情况

### 3.2 API层面改造

#### 更新现有API
```typescript
// app/api/get-user-info/route.ts
export async function GET() {
  try {
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("no auth");
    }

    const user = await findUserByUuid(userUuid);
    const quotaInfo = await QuotaService.getUserQuotaInfo(userUuid);
    
    return respData({
      user: {
        ...user,
        quota: quotaInfo,  // 新增配额信息
        credits: quotaInfo.remainingCredits  // 保持兼容性
      }
    });
  } catch (e) {
    return respErr("get user info failed");
  }
}
```

#### 新增PDF处理API
```typescript
// app/api/pdf/convert-to-quiz/route.ts
export async function POST(req: Request) {
  try {
    const { pdfData, pagesCount } = await req.json();
    const userUuid = await getUserUuid();
    
    // 估算字符数（简化算法：每页约1000字符）
    const estimatedCharacters = pagesCount * 1000;
    
    // 检查配额
    const quotaCheck = await QuotaService.checkPdfProcessingQuota(
      userUuid, 
      estimatedCharacters, 
      pagesCount
    );
    
    if (!quotaCheck.hasQuota) {
      return respErr(quotaCheck.message, {
        upgradeRequired: quotaCheck.upgradeRequired
      });
    }
    
    // 处理PDF（实际字符数计算）
    const processResult = await processPdfToQuiz(pdfData);
    const actualCharacters = processResult.extractedText.length;
    
    // 消费配额
    const consumeResult = await QuotaService.consumePdfProcessingQuota(
      userUuid, 
      actualCharacters, 
      pagesCount
    );
    
    if (!consumeResult.success) {
      return respErr("Failed to consume quota");
    }
    
    return respData({
      quiz: processResult.quiz,
      charactersProcessed: actualCharacters,
      remainingQuota: {
        characters: consumeResult.remainingCharacters,
        credits: consumeResult.remainingCredits,
      }
    });
    
  } catch (e) {
    return respErr("PDF processing failed");
  }
}
```

### 3.3 前端组件适配

#### 配额显示组件
```typescript
// components/quota/QuotaDisplay.tsx
import { QuotaDisplayInfo } from '@/types/quota';

interface Props {
  quota: QuotaDisplayInfo;
}

export default function QuotaDisplay({ quota }: Props) {
  return (
    <div className="quota-display">
      <div className="quota-info">
        <span className="plan-badge">{quota.plan}</span>
        <span className="quota-text">{quota.displayText}</span>
      </div>
      
      <div className="quota-progress">
        <div 
          className="progress-bar"
          style={{ 
            width: `${(quota.remainingCharacters / quota.totalCharacters) * 100}%` 
          }}
        />
      </div>
      
      <div className="quota-limits">
        {quota.pdfLimitPerUpload && (
          <span>Max {quota.pdfLimitPerUpload} pages per upload</span>
        )}
        {quota.maxUploadsPerMonth && (
          <span>Max {quota.maxUploadsPerMonth} uploads per month</span>
        )}
      </div>
    </div>
  );
}
```

#### 更新我的积分页面
```typescript
// app/[locale]/(default)/(console)/my-credits/page.tsx
export default async function MyCreditsPage() {
  const userUuid = await getUserUuid();
  const quotaInfo = await QuotaService.getUserQuotaInfo(userUuid);
  
  return (
    <div>
      <QuotaDisplay quota={quotaInfo} />
      
      {/* 现有积分记录表格保持不变 */}
      <TableSlot {...table} />
    </div>
  );
}
```

### 3.4 国际化与前端提示

- 统一错误码与文案，实现国际化支持
- 使用错误码映射表实现前端提示
- 支持动态更新错误码与文案

---

## 📊 迁移影响评估

### 4.1 数据兼容性 ✅
- **无数据迁移需求**：现有用户积分数据完全保留
- **向后兼容**：现有API继续正常工作
- **平滑过渡**：用户无感知切换

### 4.2 API兼容性 ✅
- **保持现有路径**：`/api/get-user-info`等API路径不变
- **响应格式兼容**：添加新字段，保留旧字段
- **客户端兼容**：现有前端代码无需立即修改

### 4.3 业务逻辑复杂度 ⚠️
- **转换逻辑**：需要在多处添加字符数与积分转换
- **配额规则**：需要实现页数限制等新逻辑
- **测试覆盖**：需要完整测试转换逻辑的正确性

---

## 🚀 实施步骤

### 阶段1：基础架构搭建（1-2天）
1. 创建 `QuotaAdapter` 适配器类
2. 定义新的类型接口
3. 创建 `QuotaService` 服务类
4. 编写单元测试

### 阶段2：API层适配（2-3天）
1. 更新 `/api/get-user-info` 返回配额信息
2. 实现 `/api/pdf/convert-to-quiz` 核心API
3. 更新现有积分消费API
4. 集成测试

### 阶段3：前端组件开发（2-3天）
1. 开发配额显示组件
2. 更新定价页面文案
3. 修改我的积分页面
4. 添加配额不足提示

### 阶段4：端到端测试（1-2天）
1. 完整流程测试
2. 边界条件测试
3. 性能测试
4. 用户验收测试

---

## 📈 性能优化建议

### 5.1 缓存策略
```typescript
// lib/quotaCache.ts
export class QuotaCache {
  private static cache = new Map<string, QuotaDisplayInfo>();
  private static readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟

  static async getUserQuota(userUuid: string): Promise<QuotaDisplayInfo> {
    const cached = this.cache.get(userUuid);
    if (cached && this.isValid(cached)) {
      return cached;
    }

    const quota = await QuotaService.getUserQuotaInfo(userUuid);
    this.cache.set(userUuid, { ...quota, _timestamp: Date.now() });
    return quota;
  }

  private static isValid(cached: any): boolean {
    return (Date.now() - cached._timestamp) < this.CACHE_TTL;
  }
}
```

### 5.2 批量操作优化
- 月度配额重置使用批量更新
- 配额检查合并多个验证逻辑
- 异步记录使用量统计

---

## 🔒 风险控制

### 6.1 数据一致性
- 事务性操作确保积分扣减的原子性
- 定期对账检查积分与实际使用量一致性
- 异常情况回滚机制

### 6.2 安全性考虑
- 防止配额检查与消费之间的竞态条件
- 客户端配额显示仅用于UI，服务端强制验证
- API调用频率限制

### 6.3 监控告警
- 配额异常消费监控
- 转换逻辑错误告警
- 性能指标监控

---

## 📝 交付物清单

1. **技术架构文档**（本文档）
2. **QuotaAdapter 适配器类**
3. **QuotaService 服务类**
4. **类型定义文件**
5. **API接口实现**
6. **前端组件库**
7. **单元测试用例**
8. **集成测试用例**
9. **性能测试报告**
10. **用户手册更新**

---

## 🎯 成功标准

- ✅ 现有用户数据100%保留
- ✅ API向后兼容性100%
- ✅ 配额转换精度≥99.9%
- ✅ 页面响应时间<2秒
- ✅ 配额检查准确率100%
- ✅ 支付流程0故障
- ✅ 用户体验无感知迁移

---

## 📞 后续支持

### 维护计划
- 定期检查转换逻辑准确性
- 监控系统性能指标
- 收集用户反馈并优化

### 扩展性考虑
- 为未来新计划类型预留扩展点
- 支持更复杂的配额规则
- 为API配额管理预留接口

---

## 📝 测试覆盖与质量保证

### 单元测试
- 测试配额转换逻辑的正确性
- 测试配额检查逻辑的正确性
- 测试配额消费逻辑的正确性
- 测试并发控制逻辑的正确性

### 集成测试
- 测试API接口的正确性
- 测试前端组件的正确性
- 测试数据库操作的正确性
- 测试配额重置逻辑的正确性

### E2E测试
- 测试完整流程的正确性
- 测试边界条件的正确性
- 测试性能指标的正确性
- 测试用户体验的正确性

---

*此方案设计确保了最小化改动、最大化复用的目标，同时为未来功能扩展预留了充分的灵活性。* 