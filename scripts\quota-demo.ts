/**
 * 配额系统演示脚本
 * 展示配额适配器的工作原理和转换逻辑
 */

import { QuotaAdapter } from '../lib/quotaAdapter';

console.log('🎯 配额系统演示\n');

// 1. 字符数与积分转换演示
console.log('1️⃣ 字符数与积分转换:');
console.log(`1000字符 = ${QuotaAdapter.charactersToCredits(1000)} 积分`);
console.log(`1500字符 = ${QuotaAdapter.charactersToCredits(1500)} 积分 (向上取整)`);
console.log(`500字符 = ${QuotaAdapter.charactersToCredits(500)} 积分 (向上取整)`);
console.log(`100积分 = ${QuotaAdapter.creditsToCharacters(100)} 字符`);
console.log(`200积分 = ${QuotaAdapter.creditsToCharacters(200)} 字符\n`);

// 2. 免费用户配额显示演示
console.log('2️⃣ 免费用户配额显示:');
const freeUser = {
  id: 1,
  uuid: 'free-user-uuid',
  email: '<EMAIL>',
  credits: {
    left_credits: 150, // 剩余150积分
    is_recharged: false
  }
} as any;

const freeQuota = QuotaAdapter.getQuotaDisplay(freeUser);
console.log(`计划: ${freeQuota.plan}`);
console.log(`剩余字符: ${freeQuota.remainingCharacters.toLocaleString()}`);
console.log(`总字符配额: ${freeQuota.totalCharacters.toLocaleString()}`);
console.log(`显示文本: ${freeQuota.displayText}`);
console.log(`单次上传限制: ${freeQuota.pdfLimitPerUpload} 页`);
console.log(`月度上传限制: ${freeQuota.maxUploadsPerMonth} 次\n`);

// 3. Plus用户配额显示演示
console.log('3️⃣ Plus用户配额显示:');
const plusUser = {
  id: 2,
  uuid: 'plus-user-uuid',
  email: '<EMAIL>',
  credits: {
    left_credits: 3500, // 剩余3500积分
    is_recharged: true
  }
} as any;

const plusQuota = QuotaAdapter.getQuotaDisplay(plusUser);
console.log(`计划: ${plusQuota.plan}`);
console.log(`剩余字符: ${plusQuota.remainingCharacters.toLocaleString()}`);
console.log(`总字符配额: ${plusQuota.totalCharacters.toLocaleString()}`);
console.log(`显示文本: ${plusQuota.displayText}`);
console.log(`单次上传限制: ${plusQuota.pdfLimitPerUpload || '无限制'}`);
console.log(`月度页数限制: ${plusQuota.maxPagesPerMonth} 页\n`);

// 4. 配额检查场景演示
console.log('4️⃣ 配额检查场景演示:');

// 模拟配额检查函数（简化版）
function simulateQuotaCheck(userType: 'FREE' | 'PLUS', charactersNeeded: number, pagesCount: number) {
  const configs = {
    FREE: { credits: 150, pdfLimitPerUpload: 5 },
    PLUS: { credits: 3500, pdfLimitPerUpload: null }
  };
  
  const config = configs[userType];
  const creditsNeeded = QuotaAdapter.charactersToCredits(charactersNeeded);
  const hasEnoughCredits = config.credits >= creditsNeeded;
  
  let pageCheckPassed = true;
  if (config.pdfLimitPerUpload && pagesCount > config.pdfLimitPerUpload) {
    pageCheckPassed = false;
  }
  
  return {
    hasQuota: hasEnoughCredits && pageCheckPassed,
    creditsNeeded,
    remainingCredits: config.credits,
    message: hasEnoughCredits ? 
      (pageCheckPassed ? 'Quota sufficient' : `Page limit exceeded (max ${config.pdfLimitPerUpload} pages)`) :
      'Insufficient quota'
  };
}

// 场景1: 免费用户上传小文件
console.log('场景1: 免费用户上传5页PDF (50K字符)');
const scenario1 = simulateQuotaCheck('FREE', 50000, 5);
console.log(`结果: ${scenario1.hasQuota ? '✅ 允许' : '❌ 拒绝'}`);
console.log(`消息: ${scenario1.message}`);
console.log(`需要积分: ${scenario1.creditsNeeded}, 剩余积分: ${scenario1.remainingCredits}\n`);

// 场景2: 免费用户上传大文件（超页数限制）
console.log('场景2: 免费用户上传15页PDF (150K字符)');
const scenario2 = simulateQuotaCheck('FREE', 150000, 15);
console.log(`结果: ${scenario2.hasQuota ? '✅ 允许' : '❌ 拒绝'}`);
console.log(`消息: ${scenario2.message}`);
console.log(`需要积分: ${scenario2.creditsNeeded}, 剩余积分: ${scenario2.remainingCredits}\n`);

// 场景3: 免费用户上传超配额文件
console.log('场景3: 免费用户上传5页PDF但字符数超配额 (200K字符)');
const scenario3 = simulateQuotaCheck('FREE', 200000, 5);
console.log(`结果: ${scenario3.hasQuota ? '✅ 允许' : '❌ 拒绝'}`);
console.log(`消息: ${scenario3.message}`);
console.log(`需要积分: ${scenario3.creditsNeeded}, 剩余积分: ${scenario3.remainingCredits}\n`);

// 场景4: Plus用户上传大文件
console.log('场景4: Plus用户上传30页PDF (300K字符)');
const scenario4 = simulateQuotaCheck('PLUS', 300000, 30);
console.log(`结果: ${scenario4.hasQuota ? '✅ 允许' : '❌ 拒绝'}`);
console.log(`消息: ${scenario4.message}`);
console.log(`需要积分: ${scenario4.creditsNeeded}, 剩余积分: ${scenario4.remainingCredits}\n`);

// 5. 配额消费演示
console.log('5️⃣ 配额消费演示:');
console.log('消费50K字符:');
console.log(`消费前积分: 150`);
console.log(`消费积分: ${QuotaAdapter.charactersToCredits(50000)}`);
console.log(`消费后积分: ${150 - QuotaAdapter.charactersToCredits(50000)}`);
console.log(`剩余字符配额: ${QuotaAdapter.creditsToCharacters(150 - QuotaAdapter.charactersToCredits(50000)).toLocaleString()}\n`);

console.log('✅ 配额系统演示完成！');
console.log('\n📝 总结:');
console.log('- 1积分 = 1000字符');
console.log('- 免费版: 200积分/月 (0.2M字符), 单次5页限制');
console.log('- Plus版: 5000积分/月 (5M字符), 月度50页限制');
console.log('- 系统自动进行字符数与积分的转换');
console.log('- 保持与现有积分系统的完全兼容性');
