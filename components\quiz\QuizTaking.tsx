'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { Quiz, QuizQuestion, QuizAttempt } from "@/types/quiz";
import { 
  Clock, 
  FileText, 
  CheckCircle,
  XCircle,
  Edit3,
  AlertCircle,
  ArrowLeft,
  ArrowRight,
  Flag
} from "lucide-react";

interface QuizTakingProps {
  quiz: Quiz;
  onComplete: (attempt: QuizAttempt) => void;
  onExit?: () => void;
}

export default function QuizTaking({ quiz, onComplete, onExit }: QuizTakingProps) {
  // 状态管理
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [timeSpent, setTimeSpent] = useState(0);
  const [startTime] = useState(Date.now());
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<string>>(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 计时器
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(timer);
  }, [startTime]);

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / quiz.questions.length) * 100;
  const answeredCount = Object.keys(answers).length;

  // 处理答案选择
  const handleAnswerChange = (questionId: string, answer: string | string[]) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  // 标记题目
  const toggleFlag = (questionId: string) => {
    setFlaggedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
        toast.success('Bookmark removed');
      } else {
        newSet.add(questionId);
        toast.success('Question bookmarked');
      }
      return newSet;
    });
  };

  // 导航函数
  const goToQuestion = (index: number) => {
    if (index >= 0 && index < quiz.questions.length) {
      setCurrentQuestionIndex(index);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const goToNext = () => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // 计算分数
  const calculateScore = (): { score: number; totalPoints: number } => {
    let score = 0;
    let totalPoints = 0;

    quiz.questions.forEach(question => {
      const points = question.points || 1;
      totalPoints += points;

      const userAnswer = answers[question.id];
      if (!userAnswer) return;

      // 根据题目类型判断答案正确性
      switch (question.type) {
        case 'mcq':
          const correctOption = question.options?.find(opt => opt.isCorrect);
          if (correctOption && userAnswer === correctOption.id) {
            score += points;
          }
          break;
        case 'true_false':
          if (userAnswer === question.correctAnswer) {
            score += points;
          }
          break;
        case 'short_answer':
          // 简答题需要更复杂的评分逻辑，这里简化处理
          if (userAnswer && question.correctAnswer) {
            const similarity = calculateTextSimilarity(
              userAnswer as string, 
              question.correctAnswer
            );
            if (similarity > 0.7) { // 70%相似度认为正确
              score += points;
            }
          }
          break;
      }
    });

    return { score, totalPoints };
  };

  // 简单的文本相似度计算（实际项目中可以使用更复杂的算法）
  const calculateTextSimilarity = (text1: string, text2: string): number => {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    const intersection = words1.filter(word => words2.includes(word));
    return intersection.length / Math.max(words1.length, words2.length);
  };

  // 提交测验
  const handleSubmit = async () => {
    const unansweredQuestions = quiz.questions.filter(q => !answers[q.id]);
    
    if (unansweredQuestions.length > 0) {
      const confirmed = window.confirm(
        `You have ${unansweredQuestions.length} unanswered questions. Are you sure you want to submit?`
      );
      if (!confirmed) return;
    }

    setIsSubmitting(true);
    try {
      const { score, totalPoints } = calculateScore();
      const percentage = Math.round((score / totalPoints) * 100);

      const attempt: QuizAttempt = {
        id: `attempt_${Date.now()}`,
        quizId: quiz.id,
        answers,
        score,
        totalPoints,
        percentage,
        timeSpent,
        startedAt: new Date(startTime),
        completedAt: new Date(),
        isCompleted: true
      };

      await onComplete(attempt);
      toast.success('Quiz submitted successfully!');
    } catch (error) {
      toast.error('Submission failed, please try again later');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'mcq': return <CheckCircle className="w-4 h-4" />;
      case 'true_false': return <XCircle className="w-4 h-4" />;
      case 'short_answer': return <Edit3 className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getTypeName = (type: string) => {
    switch (type) {
      case 'mcq': return 'Multiple Choice';
      case 'true_false': return 'True/False';
      case 'short_answer': return 'Short Answer';
      case 'essay': return 'Essay';
      default: return 'Unknown Type';
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto space-y-6">
      {/* 测验头部信息 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">{quiz.title}</CardTitle>
              {quiz.description && (
                <p className="text-muted-foreground mt-2">{quiz.description}</p>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Time</p>
                <p className="font-mono text-lg font-semibold">{formatTime(timeSpent)}</p>
              </div>
              {onExit && (
                <Button variant="outline" onClick={onExit}>
                  Exit Quiz
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 进度条 */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span>Progress: {currentQuestionIndex + 1} / {quiz.questions.length}</span>
              <span>Answered: {answeredCount} / {quiz.questions.length}</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* 题目导航 */}
          <div className="flex flex-wrap gap-2">
            {quiz.questions.map((question, index) => {
              const isAnswered = !!answers[question.id];
              const isCurrent = index === currentQuestionIndex;
              const isFlagged = flaggedQuestions.has(question.id);

              return (
                <Button
                  key={question.id}
                  variant={isCurrent ? "default" : isAnswered ? "secondary" : "outline"}
                  size="sm"
                  className={`relative ${isFlagged ? 'ring-2 ring-orange-400' : ''}`}
                  onClick={() => goToQuestion(index)}
                >
                  {index + 1}
                  {isFlagged && (
                    <Flag className="w-3 h-3 absolute -top-1 -right-1 text-orange-500" />
                  )}
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 当前题目 */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 题目内容 */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-800 rounded-full text-lg font-semibold">
                    {currentQuestionIndex + 1}
                  </span>
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(currentQuestion.type)}
                    <Badge variant="secondary" className="text-xs">
                      {getTypeName(currentQuestion.type)}
                    </Badge>
                    <Badge variant="outline" className={`text-xs ${getDifficultyColor(currentQuestion.difficulty || 'medium')}`}>
                      {currentQuestion.difficulty === 'easy' ? 'Easy' : 
                       currentQuestion.difficulty === 'medium' ? 'Medium' : 
                       currentQuestion.difficulty === 'hard' ? 'Hard' : 'Unknown'}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {currentQuestion.points || 1} 分
                    </span>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleFlag(currentQuestion.id)}
                  className={flaggedQuestions.has(currentQuestion.id) ? 'text-orange-500' : ''}
                >
                  <Flag className="w-4 h-4" />
                </Button>
              </div>
              <h3 className="text-lg font-medium mt-4">{currentQuestion.text}</h3>
            </CardHeader>
            
            <CardContent>
              <Separator className="mb-6" />
              
              {/* 选择题选项 */}
              {currentQuestion.type === 'mcq' && currentQuestion.options && (
                <RadioGroup
                  value={answers[currentQuestion.id] as string || ''}
                  onValueChange={(value) => handleAnswerChange(currentQuestion.id, value)}
                  className="space-y-3"
                >
                  {currentQuestion.options.map((option) => (
                    <div key={option.id} className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                      <RadioGroupItem value={option.id} id={option.id} />
                      <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                        <span className="font-medium mr-2">{option.id.toUpperCase()}.</span>
                        {option.text}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}

              {/* 判断题 */}
              {currentQuestion.type === 'true_false' && (
                <RadioGroup
                  value={answers[currentQuestion.id] as string || ''}
                  onValueChange={(value) => handleAnswerChange(currentQuestion.id, value)}
                  className="space-y-3"
                >
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="true" id="true" />
                    <Label htmlFor="true" className="flex-1 cursor-pointer">True</Label>
                  </div>
                  <div className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                    <RadioGroupItem value="false" id="false" />
                    <Label htmlFor="false" className="flex-1 cursor-pointer">False</Label>
                  </div>
                </RadioGroup>
              )}

              {/* 简答题 */}
              {currentQuestion.type === 'short_answer' && (
                <div className="space-y-2">
                  <Label htmlFor="answer">Please enter your answer:</Label>
                  <Textarea
                    id="answer"
                    placeholder="Please enter your answer here..."
                    value={answers[currentQuestion.id] as string || ''}
                    onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                    className="min-h-[120px]"
                  />
                </div>
              )}

              {/* 导航按钮 */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={goToPrevious}
                  disabled={currentQuestionIndex === 0}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>

                {currentQuestionIndex === quiz.questions.length - 1 ? (
                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
                  </Button>
                ) : (
                  <Button
                    onClick={goToNext}
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏信息 */}
        <div className="space-y-4">
          {/* 测验信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quiz Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4 text-blue-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Questions</p>
                  <p className="font-semibold">{quiz.totalQuestions}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Points</p>
                  <p className="font-semibold">{quiz.totalPoints}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-orange-500" />
                <div>
                  <p className="text-sm text-muted-foreground">Time Spent</p>
                  <p className="font-semibold font-mono">{formatTime(timeSpent)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 答题状态 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Answer Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Answered</span>
                  <span className="text-sm font-semibold text-green-600">
                    {answeredCount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Unanswered</span>
                  <span className="text-sm font-semibold text-gray-600">
                    {quiz.questions.length - answeredCount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Bookmarked</span>
                  <span className="text-sm font-semibold text-orange-600">
                    {flaggedQuestions.size}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 快速提交 */}
          <Card>
            <CardContent className="pt-6">
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
              </Button>
              <p className="text-xs text-muted-foreground mt-2 text-center">
                Cannot modify answers after submission
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 