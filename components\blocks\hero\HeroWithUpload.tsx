'use client';

import React, { useState } from 'react';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { FileUploader } from "@/components/upload/FileUploader";
import QuizResults from "@/components/quiz/QuizResults";
import HappyUsers from "./happy-users";
import { Hero as HeroType } from "@/types/blocks/hero";
import { Quiz } from "@/types/quiz";
import { ArrowLeft } from "lucide-react";

interface HeroWithUploadProps {
  hero: HeroType;
}

export default function HeroWithUpload({ hero }: HeroWithUploadProps) {
  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null);
  const [showResults, setShowResults] = useState(false);

  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  const handleFileUpload = (file: File) => {
    console.log('File uploaded:', file.name);
  };

  const handleQuizGenerated = (quiz: Quiz) => {
    console.log('Quiz generated:', quiz);
    setGeneratedQuiz(quiz);
    setShowResults(true);
  };

  const handleBackToUpload = () => {
    setShowResults(false);
    setGeneratedQuiz(null);
  };

  const handleExport = (format: string) => {
    console.log('Export quiz in format:', format);
    // TODO: 实现导出功能
  };

  const handleShare = () => {
    console.log('Share quiz');
    // TODO: 实现分享功能
  };

  const handleEdit = (questionId: string) => {
    console.log('Edit question:', questionId);
    // TODO: 实现编辑功能
  };

  return (
    <section id="hero" className="py-24">
      <div className="container">
        {showResults && generatedQuiz ? (
          // 显示测验结果
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                onClick={handleBackToUpload}
                className="mb-6"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                返回上传
              </Button>
            </div>

            <QuizResults
              quiz={generatedQuiz}
              onEdit={handleEdit}
              onExport={handleExport}
              onShare={handleShare}
            />
          </div>
        ) : (
          // 显示上传界面
          <>
            {/* Badge */}
            {hero.show_badge && (
              <div className="flex items-center justify-center mb-8">
                <img
                  src="/imgs/badges/phdaily.svg"
                  alt="phdaily"
                  className="h-10 object-cover"
                />
              </div>
            )}

            <div className="text-center mb-12">
              {/* 公告 */}
              {hero.announcement && (
                <a
                  href={hero.announcement.url}
                  className="mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
                >
                  {hero.announcement.label && (
                    <Badge>{hero.announcement.label}</Badge>
                  )}
                  {hero.announcement.title}
                </a>
              )}

              {/* 标题 */}
              {texts && texts.length > 1 ? (
                <h1 className="mx-auto mb-6 mt-4 max-w-5xl text-balance text-4xl font-bold lg:mb-8 lg:text-6xl">
                  {texts[0]}
                  <span className="bg-gradient-to-r from-primary via-primary to-primary bg-clip-text text-transparent">
                    {highlightText}
                  </span>
                  {texts[1]}
                </h1>
              ) : (
                <h1 className="mx-auto mb-6 mt-4 max-w-4xl text-balance text-4xl font-bold lg:mb-8 lg:text-6xl">
                  {hero.title}
                </h1>
              )}

              {/* 描述 */}
              <p
                className="mx-auto max-w-3xl text-muted-foreground lg:text-xl mb-12"
                dangerouslySetInnerHTML={{ __html: hero.description || "" }}
              />
            </div>

            {/* 文件上传组件 */}
            <FileUploader
              onFileUpload={handleFileUpload}
              onQuizGenerated={handleQuizGenerated}
            />

            {/* 用户统计 */}
            {hero.show_happy_users && (
              <div className="mt-12">
                <HappyUsers />
              </div>
            )}

            {/* 提示文字 */}
            {hero.tip && (
              <p className="mt-8 text-md text-muted-foreground text-center">{hero.tip}</p>
            )}
          </>
        )}
      </div>
    </section>
  );
}
