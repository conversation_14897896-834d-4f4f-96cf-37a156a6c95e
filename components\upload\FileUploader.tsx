'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from 'sonner';
import { FileText } from 'lucide-react';

interface FileUploaderProps {
  onFileUpload?: (file: File) => void;
  onQuizGenerated?: (quiz: any) => void;
}

export const FileUploader: React.FC<FileUploaderProps> = ({
  onFileUpload,
  onQuizGenerated
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  }, []);

  const handleFileUpload = async (file: File) => {
    // 更新文件验证 - 只保留真正支持的格式
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
      'text/plain',
      'image/jpeg',
      'image/png', 
      'image/bmp',
      'image/tiff'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a supported file format (PDF, DOCX, TXT, JPG, PNG, BMP, TIFF)');
      return;
    }

    if (file.size > 20 * 1024 * 1024) { // 20MB limit
      toast.error('File size must be less than 20MB');
      return;
    }

    setUploadedFile(file);
    if (onFileUpload) {
      onFileUpload(file);
    }
    
    // 根据文件类型显示不同的成功消息
    const fileType = file.type;
    if (fileType.startsWith('image/')) {
      toast.success('Image uploaded successfully! OCR will extract text automatically.');
    } else if (fileType.includes('wordprocessingml')) {
      toast.success('Word document uploaded successfully!');
    } else {
      toast.success('File uploaded successfully!');
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileUpload(files[0]);
    }
  };

  const convertToQuiz = async () => {
    if (!uploadedFile) {
      toast.error('Please upload a file first');
      return;
    }

    setIsGenerating(true);

    try {
      const formData = new FormData();
      formData.append('file', uploadedFile);

      // 添加生成选项
      const options = {
        questionCount: 5,
        questionTypes: ['mcq', 'true_false'],
        difficulty: 'medium',
        language: 'en',
        includeExplanations: true
      };
      formData.append('options', JSON.stringify(options));

      const response = await fetch('/api/pdf/convert-to-quiz', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      // 配额不足的特殊处理
      if (response.status === 402 && result.error === '配额不足') {
        const quotaInfo = result.quotaInfo || {};
        const remainingChars = quotaInfo.remainingCharacters || 0;
        const creditsNeeded = quotaInfo.creditsNeeded || 0;
        
        toast.error(
          <div className="space-y-2">
            <p className="font-semibold">配额不足，无法处理此文件</p>
            <p className="text-sm">剩余配额: {(remainingChars / 1000).toFixed(1)}K 字符</p>
            <p className="text-sm">需要配额: {creditsNeeded} 积分</p>
            <div className="flex space-x-2 mt-2">
              <button 
                onClick={() => window.open('/#pricing', '_blank')}
                className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
              >
                升级套餐
              </button>
              <button 
                onClick={() => window.open('/my-credits', '_blank')}
                className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600"
              >
                查看配额
              </button>
            </div>
          </div>,
          { duration: 6000 }
        );
        return;
      }

      // 其他API错误处理
      if (!response.ok) {
        const errorMessage = result.error || '生成测验失败';
        toast.error(errorMessage);
        return;
      }

      if (result.success && result.quiz) {
        if (onQuizGenerated) {
          onQuizGenerated(result.quiz);
        }
        toast.success(`Quiz generated successfully! Contains ${result.quiz.totalQuestions} questions`);

        // 显示一些元数据信息
        if (result.metadata?.warnings && result.metadata.warnings.length > 0) {
          result.metadata.warnings.forEach((warning: string) => {
            toast.warning(warning);
          });
        }
      } else {
        toast.error('生成的测验数据无效');
      }

    } catch (error) {
      console.error('Quiz generation error:', error);
      // 网络错误或其他异常
      toast.error('Network connection error, please check your network and try again');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto space-y-6">
      {/* 文件上传区域 */}
      <Card
        className={`p-8 border-2 border-dashed transition-colors ${
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-gray-300 hover:border-primary/50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <div className="text-center space-y-4">
          {/* PDF图标 */}
          <div className="flex justify-center items-center space-x-4">
            <div className="w-16 h-16 bg-slate-100 rounded-lg flex items-center justify-center">
              <FileText className="w-8 h-8 text-slate-600" />
            </div>
            <div className="text-4xl">→</div>
            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              {uploadedFile ? uploadedFile.name : 'Click to upload or drag and drop'}
            </h3>
            <p className="text-gray-500 mt-2">
              PDF, DOCX, TXT, JPG, PNG, BMP, TIFF files supported
            </p>
            <p className="text-gray-500 text-sm">up to 20MB</p>
          </div>

          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileInputChange}
            accept=".pdf,.docx,.txt,.jpg,.jpeg,.png,.bmp,.tiff"
          />

          <Button
            onClick={() => document.getElementById('file-upload')?.click()}
            variant="outline"
            disabled={isUploading}
          >
            {isUploading ? 'Uploading...' : 'Select files'}
          </Button>
        </div>
      </Card>

      {/* 转换按钮 */}
      {uploadedFile && (
        <div className="text-center">
          <Button
            onClick={convertToQuiz}
            disabled={isGenerating}
            className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 text-lg"
            size="lg"
          >
            {isGenerating ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Please wait, your quiz is being created...</span>
              </div>
            ) : (
              <>Convert to Quiz →</>
            )}
          </Button>
        </div>
      )}

      {/* 安全提示 */}
      <p className="text-sm text-gray-500 text-center">
        🔒 Your PDFs will be safely handled by our servers and deleted after the quiz creation.
      </p>
    </div>
  );
};
