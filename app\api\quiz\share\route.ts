import { NextRequest, NextResponse } from 'next/server';
import { Quiz } from '@/types/quiz';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';

// 简单的文件存储方案（生产环境应该用数据库）
const SHARE_DIR = join(process.cwd(), '.share-data');

export async function POST(request: NextRequest) {
  try {
    const { quiz, options = {} } = await request.json();
    
    if (!quiz || !quiz.questions) {
      return NextResponse.json(
        { error: 'Invalid quiz data' },
        { status: 400 }
      );
    }

    // 生成分享ID
    const shareId = `quiz_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 准备分享数据
    const shareData = {
      quiz: {
        ...quiz,
        // 根据选项决定是否包含答案
        questions: quiz.questions.map((q: any) => ({
          ...q,
          ...(options.includeAnswers ? {} : {
            correctAnswer: undefined,
            options: q.options?.map((opt: any) => ({
              ...opt,
              isCorrect: undefined
            }))
          })
        }))
      },
      sharedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天后过期
      options
    };

    // 确保目录存在
    try {
      await mkdir(SHARE_DIR, { recursive: true });
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    // 保存到文件
    const filePath = join(SHARE_DIR, `${shareId}.json`);
    await writeFile(filePath, JSON.stringify(shareData, null, 2));

    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 
                   (process.env.NODE_ENV === 'production' ? 'https://your-domain.com' : 'http://localhost:3000');
    
    const shareUrl = `${baseUrl}/quiz/shared/${shareId}`;
    
    return NextResponse.json({ 
      success: true, 
      shareUrl,
      shareId,
      expiresAt: shareData.expiresAt
    });

  } catch (error) {
    console.error('Share creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create share link' },
      { status: 500 }
    );
  }
}

// 获取分享的测验
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const shareId = searchParams.get('id');
    
    if (!shareId) {
      return NextResponse.json(
        { error: 'Share ID required' },
        { status: 400 }
      );
    }

    const filePath = join(SHARE_DIR, `${shareId}.json`);
    
    try {
      const fileContent = await readFile(filePath, 'utf-8');
      const shareData = JSON.parse(fileContent);
      
      // 检查是否过期
      if (new Date() > new Date(shareData.expiresAt)) {
        return NextResponse.json(
          { error: 'Share link has expired' },
          { status: 410 }
        );
      }
      
      return NextResponse.json({
        success: true,
        quiz: shareData.quiz,
        sharedAt: shareData.sharedAt,
        expiresAt: shareData.expiresAt
      });
      
    } catch (fileError) {
      return NextResponse.json(
        { error: 'Share not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('Share retrieval error:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve share' },
      { status: 500 }
    );
  }
}
