# Creem.io 产品配置指南

## 🚨 问题诊断

当前错误：`Product not found` - 产品 ID `prod_5hYNGlM2t3DdeKqs6AjW5K` 在 Creem.io 后台不存在。

## 🛠️ 解决步骤

### 第一步：登录 Creem.io 后台

1. 访问 [https://creem.io/dashboard](https://creem.io/dashboard)
2. 确保你在 **测试模式** (Test Mode) 下
3. 导航到 **Products** 页面

### 第二步：创建免费版产品

1. 点击 **"Create Product"** 按钮
2. 填写产品信息：
   ```
   产品名称: Free PDF to Quiz Plan
   描述: Perfect for trying our PDF to quiz converter. 5 conversions per month.
   价格: $0.00
   货币: USD
   计费类型: One-time (一次性付费)
   ```
3. 点击 **"Create"** 创建产品
4. 创建后，点击产品的 **"Options"** → **"Copy ID"**
5. 复制产品 ID (格式类似: `prod_xxxxxxxxxx`)

### 第三步：创建专业版产品

1. 再次点击 **"Create Product"** 按钮
2. 填写产品信息：
   ```
   产品名称: Pro Quiz Generator Plan
   描述: For educators and power users. Unlimited conversions and advanced features.
   价格: $14.90
   货币: USD
   计费类型: Recurring (订阅)
   计费周期: Monthly (每月)
   ```
3. 点击 **"Create"** 创建产品
4. 创建后，点击产品的 **"Options"** → **"Copy ID"**
5. 复制产品 ID

### 第四步：更新环境变量

将获得的产品 ID 更新到 `.env.local` 文件中：

```bash
# 产品配置
FREE_PLAN_PRODUCT_ID = prod_你的免费版产品ID
PRO_PLAN_PRODUCT_ID = prod_你的专业版产品ID
```

### 第五步：更新定价配置

需要更新以下文件中的 `product_id` 字段：

1. **英文版定价** (`i18n/pages/landing/en.json`)：
   ```json
   {
     "title": "Free",
     "product_id": "你的免费版产品ID",
     // ... 其他配置
   },
   {
     "title": "Pro", 
     "product_id": "你的专业版产品ID",
     // ... 其他配置
   }
   ```

2. **中文版定价** (`i18n/pages/landing/zh.json`)：
   ```json
   {
     "title": "免费版",
     "product_id": "你的免费版产品ID",
     // ... 其他配置
   },
   {
     "title": "专业版",
     "product_id": "你的专业版产品ID", 
     // ... 其他配置
   }
   ```

## 🔧 配置示例

### Creem.io 产品配置建议

#### 免费版产品
- **名称**: Free PDF to Quiz Plan
- **价格**: $0.00
- **类型**: One-time
- **描述**: Perfect for trying our PDF to quiz converter. Includes 5 PDF conversions per month with basic features.

#### 专业版产品  
- **名称**: Pro Quiz Generator Plan
- **价格**: $14.90
- **类型**: Recurring (Monthly)
- **描述**: For educators and power users. Unlimited PDF conversions, advanced question types, and priority support.

### 成功 URL 配置

在创建产品时，设置成功跳转 URL：
```
Success URL: http://localhost:3000/creem-success
```

## 🧪 测试步骤

1. 重启开发服务器：
   ```bash
   npm run dev
   ```

2. 访问定价页面：
   ```
   http://localhost:3000/#pricing
   ```

3. 点击任一方案的购买按钮

4. 应该能成功跳转到 Creem.io 支付页面

## 🚨 常见问题

### Q: 仍然显示 "Product not found"
**A**: 检查以下几点：
- 确保在 Creem.io 后台的测试模式下创建了产品
- 确保产品 ID 正确复制到配置文件中
- 确保 `CREEM_API_KEY` 对应测试环境

### Q: 支付页面显示错误价格
**A**: 检查定价配置中的 `amount` 字段：
- 免费版应该是 `0`
- 专业版应该是 `1490` (表示 $14.90，以分为单位)

### Q: 无法创建订阅产品
**A**: 确保在创建产品时选择了：
- 计费类型: **Recurring**
- 计费周期: **Monthly**

## 📋 检查清单

- [ ] 在 Creem.io 测试模式下创建了免费版产品
- [ ] 在 Creem.io 测试模式下创建了专业版产品  
- [ ] 复制了正确的产品 ID
- [ ] 更新了 `.env.local` 中的产品 ID
- [ ] 更新了定价配置文件中的 `product_id`
- [ ] 重启了开发服务器
- [ ] 测试了支付流程

完成以上步骤后，支付功能应该能正常工作！
