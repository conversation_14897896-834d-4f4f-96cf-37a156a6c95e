'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Quiz, QuizQuestion } from "@/types/quiz";
import { 
  Clock, 
  FileText, 
  Download, 
  Share2, 
  Edit3,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";

interface QuizResultsProps {
  quiz: Quiz;
  onEdit?: (questionId: string) => void;
  onExport?: (format: string) => void;
  onShare?: () => void;
}

export default function QuizResults({ quiz, onEdit, onExport, onShare }: QuizResultsProps) {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set());

  const toggleQuestion = (questionId: string) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionId)) {
      newExpanded.delete(questionId);
    } else {
      newExpanded.add(questionId);
    }
    setExpandedQuestions(newExpanded);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'mcq': return <CheckCircle className="w-4 h-4" />;
      case 'true_false': return <XCircle className="w-4 h-4" />;
      case 'short_answer': return <Edit3 className="w-4 h-4" />;
      default: return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getTypeName = (type: string) => {
    switch (type) {
      case 'mcq': return '选择题';
      case 'true_false': return '判断题';
      case 'short_answer': return '简答题';
      case 'essay': return '论述题';
      default: return '未知类型';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* 测验概览 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-2xl">{quiz.title}</CardTitle>
            <Badge variant="outline" className={getDifficultyColor(quiz.difficulty || 'medium')}>
              {quiz.difficulty === 'easy' ? '简单' : 
               quiz.difficulty === 'medium' ? '中等' : 
               quiz.difficulty === 'hard' ? '困难' : '未知'}
            </Badge>
          </div>
          {quiz.description && (
            <p className="text-muted-foreground">{quiz.description}</p>
          )}
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">题目数量</p>
                <p className="font-semibold">{quiz.totalQuestions}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">总分</p>
                <p className="font-semibold">{quiz.totalPoints}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-orange-500" />
              <div>
                <p className="text-sm text-muted-foreground">预计时间</p>
                <p className="font-semibold">{quiz.estimatedTime || 10} 分钟</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-sm text-muted-foreground">来源文件</p>
                <p className="font-semibold text-xs truncate">
                  {quiz.sourceFile?.name || '未知'}
                </p>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onExport?.('pdf')}
            >
              <Download className="w-4 h-4 mr-2" />
              导出PDF
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onExport?.('docx')}
            >
              <Download className="w-4 h-4 mr-2" />
              导出Word
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={onShare}
            >
              <Share2 className="w-4 h-4 mr-2" />
              分享
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 题目列表 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">测验题目</h3>
        {quiz.questions.map((question, index) => (
          <Card key={question.id} className="overflow-hidden">
            <CardHeader 
              className="cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => toggleQuestion(question.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
                    {index + 1}
                  </span>
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(question.type)}
                    <Badge variant="secondary" className="text-xs">
                      {getTypeName(question.type)}
                    </Badge>
                    <Badge variant="outline" className={`text-xs ${getDifficultyColor(question.difficulty || 'medium')}`}>
                      {question.difficulty === 'easy' ? '简单' : 
                       question.difficulty === 'medium' ? '中等' : 
                       question.difficulty === 'hard' ? '困难' : '未知'}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {question.points || 1} 分
                    </span>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit?.(question.id);
                  }}
                >
                  <Edit3 className="w-4 h-4" />
                </Button>
              </div>
              <p className="text-left font-medium mt-2">{question.text}</p>
            </CardHeader>
            
            {expandedQuestions.has(question.id) && (
              <CardContent>
                <Separator className="mb-4" />
                
                {/* 选择题选项 */}
                {question.type === 'mcq' && question.options && (
                  <div className="space-y-2 mb-4">
                    <p className="text-sm font-medium text-muted-foreground">选项：</p>
                    {question.options.map((option) => (
                      <div 
                        key={option.id} 
                        className={`p-3 rounded-lg border ${
                          option.isCorrect 
                            ? 'bg-green-50 border-green-200' 
                            : 'bg-gray-50 border-gray-200'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{option.id.toUpperCase()}.</span>
                          <span>{option.text}</span>
                          {option.isCorrect && (
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* 判断题答案 */}
                {question.type === 'true_false' && question.correctAnswer && (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-muted-foreground mb-2">正确答案：</p>
                    <Badge variant={question.correctAnswer === 'true' ? 'default' : 'secondary'}>
                      {question.correctAnswer === 'true' ? '正确' : '错误'}
                    </Badge>
                  </div>
                )}

                {/* 简答题答案 */}
                {question.type === 'short_answer' && question.correctAnswer && (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-muted-foreground mb-2">参考答案：</p>
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm">{question.correctAnswer}</p>
                    </div>
                  </div>
                )}

                {/* 答案解释 */}
                {question.explanation && (
                  <div className="mb-4">
                    <p className="text-sm font-medium text-muted-foreground mb-2">解释：</p>
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-sm">{question.explanation}</p>
                    </div>
                  </div>
                )}

                {/* 来源文本 */}
                {question.sourceText && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">来源文本：</p>
                    <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <p className="text-xs text-gray-600 italic">"{question.sourceText}"</p>
                    </div>
                  </div>
                )}
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* 标签 */}
      {quiz.tags && quiz.tags.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-sm font-medium text-muted-foreground mb-2">相关标签：</p>
            <div className="flex flex-wrap gap-2">
              {quiz.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
