import { NextRequest, NextResponse } from 'next/server';
import { PDFService } from '@/services/pdfService';
import { QuizService } from '@/services/quizService';
import { QuizGenerationOptions } from '@/types/quiz';
import { QuotaService } from '@/services/quotaService';
import { getUserUuid } from '@/services/user';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Login required to use this feature' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // 获取生成选项（如果有的话）
    const optionsStr = formData.get('options') as string;
    const defaultOptions: QuizGenerationOptions = {
      questionCount: 5,
      questionTypes: ['mcq', 'true_false'],
      difficulty: 'medium',
      language: 'en',
      includeExplanations: true
    };

    let options = defaultOptions;
    if (optionsStr) {
      try {
        const parsedOptions = JSON.parse(optionsStr);
        options = { ...defaultOptions, ...parsedOptions };
      } catch (e) {
        console.warn('解析选项失败，使用默认选项:', e);
      }
    }

    // 初始化服务
    const pdfService = PDFService.getInstance();
    const quizService = QuizService.getInstance();

    // 验证文件
    const validation = await pdfService.validateFile(file);
    if (!validation.canProcess) {
      return NextResponse.json(
        {
          error: 'File validation failed',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // 配额检查 - 估算字符数和页数
    const estimatedPages = Math.ceil(file.size / 50000); // 估算页数，不依赖 pageCount
    const estimatedCharacters = QuotaService.estimatePdfCharacters(estimatedPages, file.size);

    const quotaCheck = await QuotaService.checkPdfProcessingQuota(
      userUuid,
      estimatedCharacters,
      estimatedPages
    );

    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        {
          error: 'Insufficient quota',
          message: 'Current file size exceeds your available quota',
          details: quotaCheck.message,
          quotaInfo: {
            upgradeRequired: quotaCheck.upgradeRequired,
            remainingCharacters: quotaCheck.remainingCharacters,
            creditsNeeded: quotaCheck.creditsNeeded,
            estimatedCharacters: estimatedCharacters,
            // 添加升级建议
            suggestions: {
              upgradeUrl: '/pricing',
              quotaUrl: '/my-credits',
              planRecommendation: quotaCheck.upgradeRequired ? 'plus' : null
            }
          }
        },
        { status: 402 } // Payment Required
      );
    }

    // 处理PDF并提取内容
    const processingResult = await pdfService.processPDF(file);
    if (!processingResult.success || !processingResult.content) {
      return NextResponse.json(
        {
          error: 'File processing failed',
          details: processingResult.error
        },
        { status: 500 }
      );
    }

    // 检查内容质量
    if (processingResult.content.wordCount < 50) {
      return NextResponse.json(
        {
          error: 'Document content is too short to generate valid quiz questions',
          details: `Document contains only ${processingResult.content.wordCount} words, at least 50 words recommended`
        },
        { status: 400 }
      );
    }

    // 生成测验
    const quizResult = await quizService.generateQuiz(processingResult.content, options);

    // 验证生成的测验
    const validation_result = quizService.validateQuiz(quizResult.quiz);
    if (!validation_result.isValid) {
      return NextResponse.json(
        {
          error: 'Generated quiz validation failed',
          details: validation_result.errors
        },
        { status: 500 }
      );
    }

    // 添加源文件信息
    quizResult.quiz.sourceFile = {
      name: file.name,
      type: file.type,
      size: file.size
    };

    // 消费配额 - 基于实际提取的字符数
    const actualCharacters = processingResult.content.text?.length || estimatedCharacters;
    const actualPages = processingResult?.content?.metadata?.pageCount || estimatedPages;

    const consumeResult = await QuotaService.consumePdfProcessingQuota(
      userUuid,
      actualCharacters,
      actualPages
    );

    if (!consumeResult.success) {
      console.warn('配额消费失败，但测验已生成:', consumeResult.message);
      // 注意：这里测验已经生成，但配额消费失败
      // 在生产环境中可能需要回滚或记录异常
    }

    return NextResponse.json({
      success: true,
      quiz: quizResult.quiz,
      metadata: {
        processingTime: quizResult.processingTime,
        confidence: quizResult.confidence,
        warnings: quizResult.warnings,
        extractedWordCount: processingResult.content.wordCount,
        fileInfo: validation.fileInfo,
        // 新增配额信息
        quotaUsed: {
          charactersProcessed: actualCharacters,
          pagesProcessed: actualPages,
          remainingCharacters: consumeResult.remainingCharacters,
          remainingCredits: consumeResult.remainingCredits,
        }
      }
    });

  } catch (error) {
    console.error('Quiz generation error:', error);

    // 根据错误类型返回不同的错误信息
    let errorMessage = 'Quiz generation failed, please try again later';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('AI生成')) {
        errorMessage = 'AI service temporarily unavailable, please try again later';
        statusCode = 503;
      } else if (error.message.includes('文件')) {
        errorMessage = 'File processing failed, please check file format';
        statusCode = 400;
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error?.toString() : undefined
      },
      { status: statusCode }
    );
  }
}
