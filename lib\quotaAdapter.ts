import { getUserCredits, decreaseCredits, CreditsTransType } from '@/services/credit';
import { findUserByUuid } from '@/models/user';
import { User } from '@/types/user';
import {
  QuotaCheckResult as BaseQuotaCheckResult,
  ConsumeQuotaResult as BaseConsumeQuotaResult,
  SubscriptionPlan,
  PLAN_CONFIGS
} from '@/types/quota';

// 扩展配额显示信息接口（兼容现有积分系统）
export interface QuotaDisplayInfo {
  plan: 'FREE' | 'PLUS' | 'PRO';
  remainingCharacters: number;
  totalCharacters: number;
  remainingCredits: number;
  displayText: string;
  pdfLimitPerUpload: number | null;
  maxUploadsPerMonth: number | null;
  maxPagesPerMonth: number | null;
}

// 扩展配额检查结果接口（兼容现有积分系统）
export interface QuotaCheckResult extends BaseQuotaCheckResult {
  remainingCredits: number;
  creditsNeeded: number;
}

// 扩展配额消费结果接口（兼容现有积分系统）
export interface ConsumeQuotaResult extends BaseConsumeQuotaResult {
  remainingCredits: number;
  charactersUsed?: number;
  creditsUsed?: number;
}

/**
 * 配额适配器类 - 将现有积分系统适配为字符配额系统
 * 采用适配器模式，最小化现有代码改动
 */
export class QuotaAdapter {
  // 配额转换规则 - 基于更新的三层定价策略
  private static readonly CREDIT_CONFIGS = {
    FREE: {
      monthlyCredits: Math.ceil(PLAN_CONFIGS.free.characters_quota / 1000), // 0.2M字符 = 200积分
      charactersPerCredit: 1000,        // 1积分 = 1000字符
      pdfLimitPerUpload: PLAN_CONFIGS.free.pages_per_upload, // 5页/次
      maxPagesPerMonth: null,           // 无月度页数限制
      maxUploadsPerMonth: null,         // 无月度上传次数限制，依赖字符配额
    },
    PLUS: {
      monthlyCredits: Math.ceil(PLAN_CONFIGS.plus.characters_quota / 1000), // 5M字符 = 5000积分
      charactersPerCredit: 1000,        // 1积分 = 1000字符
      pdfLimitPerUpload: PLAN_CONFIGS.plus.pages_per_upload, // 20页/次（更新）
      maxPagesPerMonth: PLAN_CONFIGS.plus.pages_per_month,   // 200页/月（更新）
    },
    PRO: {
      monthlyCredits: Math.ceil(PLAN_CONFIGS.pro.characters_quota / 1000), // 15M字符 = 15000积分
      charactersPerCredit: 1000,        // 1积分 = 1000字符
      pdfLimitPerUpload: PLAN_CONFIGS.pro.pages_per_upload,  // 50页/次（更新）
      maxPagesPerMonth: PLAN_CONFIGS.pro.pages_per_month,    // 500页/月（更新）
    }
  };

  /**
   * 将字符数转换为积分数
   */
  static charactersToCredits(characters: number): number {
    return Math.ceil(characters / 1000);
  }

  /**
   * 将积分数转换为字符数
   */
  static creditsToCharacters(credits: number): number {
    return credits * 1000;
  }

  /**
   * 获取用户配额显示信息
   */
  static getQuotaDisplay(user: User): QuotaDisplayInfo {
    const plan = this.getUserPlan(user);
    const config = this.CREDIT_CONFIGS[plan];
    const remainingCredits = user.credits?.left_credits || 0;
    const remainingCharacters = this.creditsToCharacters(remainingCredits);
    const totalCharacters = this.creditsToCharacters(config.monthlyCredits);

    return {
      plan,
      remainingCharacters,
      totalCharacters,
      remainingCredits,
      displayText: `${(remainingCharacters / 1000).toFixed(1)}K / ${(totalCharacters / 1000).toFixed(1)}K characters`,
      pdfLimitPerUpload: config.pdfLimitPerUpload,
      maxUploadsPerMonth: config.maxUploadsPerMonth,
      maxPagesPerMonth: config.maxPagesPerMonth,
    };
  }

  /**
   * 检查是否有足够配额
   */
  static async checkQuota(
    userUuid: string,
    charactersNeeded: number,
    pagesCount?: number
  ): Promise<QuotaCheckResult> {
    const userCredits = await getUserCredits(userUuid);
    const user = await findUserByUuid(userUuid);
    const plan = this.getUserPlan(user);
    const config = this.CREDIT_CONFIGS[plan];

    const creditsNeeded = this.charactersToCredits(charactersNeeded);
    const hasEnoughCredits = userCredits.left_credits >= creditsNeeded;

    // 检查单次上传页数限制
    let pageCheckPassed = true;
    let errorMessage = '';
    
    if (config.pdfLimitPerUpload && pagesCount) {
      if (pagesCount > config.pdfLimitPerUpload) {
        pageCheckPassed = false;
        errorMessage = `Single upload page limit exceeded (max ${config.pdfLimitPerUpload} pages)`;
      }
    }

    // TODO: 检查月度页数限制（需要添加用户月度页数使用统计）
    // if (config.maxPagesPerMonth && pagesCount) {
    //   const monthlyPagesUsed = await getUserMonthlyPagesUsed(userUuid);
    //   if (monthlyPagesUsed + pagesCount > config.maxPagesPerMonth) {
    //     pageCheckPassed = false;
    //     errorMessage = `月度页数配额不足（${monthlyPagesUsed + pagesCount}/${config.maxPagesPerMonth}页）`;
    //   }
    // }

    const quotaSufficient = hasEnoughCredits && pageCheckPassed;

    return {
      hasQuota: quotaSufficient,
      remainingCharacters: this.creditsToCharacters(userCredits.left_credits),
      remainingCredits: userCredits.left_credits,
      creditsNeeded,
      message: quotaSufficient ? 'Quota sufficient' : 
        (errorMessage || 'Insufficient character quota'),
      upgradeRequired: !quotaSufficient
    };
  }

  /**
   * 消费配额（字符数）
   */
  static async consumeQuota(
    userUuid: string,
    charactersUsed: number,
    operationType: string = 'pdf_to_quiz'
  ): Promise<ConsumeQuotaResult> {
    const creditsToConsume = this.charactersToCredits(charactersUsed);

    try {
      // 复用现有积分扣减逻辑
      await decreaseCredits({
        user_uuid: userUuid,
        trans_type: operationType as CreditsTransType,
        credits: creditsToConsume,
      });

      const remainingCredits = await getUserCredits(userUuid);

      return {
        success: true,
        remainingCharacters: this.creditsToCharacters(remainingCredits.left_credits),
        remainingCredits: remainingCredits.left_credits,
        charactersUsed,
        creditsUsed: creditsToConsume,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to consume quota: ${error}`,
        remainingCharacters: 0,
        remainingCredits: 0,
      };
    }
  }

  /**
   * 获取用户计划类型 - 根据订阅产品ID和积分数量判断
   */
  private static getUserPlan(user: User): 'FREE' | 'PLUS' | 'PRO' {
    // 如果用户未充值，直接返回免费版
    if (!user.credits?.is_recharged) {
      return 'FREE';
    }

    // 通过用户的订单记录或积分数量判断计划类型
    // 方案1: 通过积分数量判断（简单但不够精确）
    const totalCredits = user.credits?.left_credits || 0;
    
    if (totalCredits >= 10000) { // 15M字符对应15000积分，设置阈值为10000
      return 'PRO';
    } else if (totalCredits >= 3000) { // 5M字符对应5000积分，设置阈值为3000
      return 'PLUS';
    }
    
    // 默认返回Plus（已充值用户的最低等级）
    return 'PLUS';
    
    // TODO: 方案2: 通过用户最近的订单记录判断（更精确）
    // 可以查询用户最近的有效订单，根据product_id判断计划类型
    // const latestOrder = await getLatestValidOrderByUserUuid(user.uuid);
    // if (latestOrder) {
    //   switch (latestOrder.product_id) {
    //     case 'pro':
    //     case process.env.PRO_PLAN_PRODUCT_ID:
    //       return 'PRO';
    //     case 'plus':
    //     case process.env.PLUS_PLAN_PRODUCT_ID:
    //       return 'PLUS';
    //     default:
    //       return 'FREE';
    //   }
    // }
  }
}
