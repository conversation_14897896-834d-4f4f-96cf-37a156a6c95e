<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="800" height="400" fill="#f8fafc" rx="12"/>
  
  <!-- 步骤1: PDF上传 -->
  <g transform="translate(50, 100)">
    <circle cx="60" cy="60" r="50" fill="#3b82f6" opacity="0.1"/>
    <text x="60" y="40" text-anchor="middle" font-size="32">📄</text>
    <text x="60" y="85" text-anchor="middle" font-size="16" fill="#1f2937">Upload PDF</text>
    <text x="60" y="105" text-anchor="middle" font-size="12" fill="#6b7280">Drag & Drop</text>
  </g>
  
  <!-- 箭头1 -->
  <path d="M170 160 L230 160" stroke="#3b82f6" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- 步骤2: AI分析 -->
  <g transform="translate(250, 100)">
    <circle cx="60" cy="60" r="50" fill="#10b981" opacity="0.1"/>
    <text x="60" y="40" text-anchor="middle" font-size="32">🤖</text>
    <text x="60" y="85" text-anchor="middle" font-size="16" fill="#1f2937">AI Analysis</text>
    <text x="60" y="105" text-anchor="middle" font-size="12" fill="#6b7280">Extract Content</text>
  </g>
  
  <!-- 箭头2 -->
  <path d="M370 160 L430 160" stroke="#10b981" stroke-width="3" marker-end="url(#arrow)"/>
  
  <!-- 步骤3: 生成测验 -->
  <g transform="translate(450, 100)">
    <circle cx="60" cy="60" r="50" fill="#f59e0b" opacity="0.1"/>
    <text x="60" y="40" text-anchor="middle" font-size="32">❓</text>
    <text x="60" y="85" text-anchor="middle" font-size="16" fill="#1f2937">Quiz Ready</text>
    <text x="60" y="105" text-anchor="middle" font-size="12" fill="#6b7280">Interactive Test</text>
  </g>
  
  <!-- 箭头定义 -->
  <defs>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto">
      <polygon points="0 0, 10 3, 0 6" fill="#3b82f6"/>
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="40" text-anchor="middle" font-size="24" font-weight="bold" fill="#1f2937">
    PDF to Quiz in 3 Simple Steps
  </text>
</svg>
