import { Quiz } from '@/types/quiz';

export interface ShareOptions {
  platform: 'link' | 'email' | 'social';
  includeAnswers?: boolean;
  expiresIn?: number; // 小时
}

export class ShareService {
  private static instance: ShareService;

  public static getInstance(): ShareService {
    if (!ShareService.instance) {
      ShareService.instance = new ShareService();
    }
    return ShareService.instance;
  }

  /**
   * 生成分享链接
   */
  public async generateShareLink(
    quiz: Quiz, 
    options: ShareOptions = { platform: 'link' }
  ): Promise<{ success: boolean; shareUrl?: string; error?: string }> {
    try {
      const response = await fetch('/api/quiz/share', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quiz,
          options: {
            includeAnswers: options.includeAnswers || false,
            expiresIn: options.expiresIn || 168 // 7天
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Share link generation failed');
      }

      const result = await response.json();
      return { 
        success: true, 
        shareUrl: result.shareUrl 
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Share failed'
      };
    }
  }

  /**
   * 复制到剪贴板
   */
  public async copyToClipboard(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.error('Copy failed:', error);
      return false;
    }
  }
} 