'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, CreditCard, Eye, ArrowUpCircle } from 'lucide-react';

interface QuotaWarningProps {
  remainingCharacters: number;
  totalCharacters: number;
  creditsNeeded: number;
  upgradeRequired?: boolean;
  onUpgrade?: () => void;
  onViewQuota?: () => void;
}

export const QuotaWarning: React.FC<QuotaWarningProps> = ({
  remainingCharacters, 
  totalCharacters, 
  creditsNeeded,
  upgradeRequired = false,
  onUpgrade,
  onViewQuota
}) => {
  const usagePercentage = ((totalCharacters - remainingCharacters) / totalCharacters) * 100;
  const isCriticalQuota = usagePercentage > 90;

  const getWarningLevel = () => {
    if (remainingCharacters === 0) return 'critical';
    if (usagePercentage > 80) return 'warning';
    return 'info';
  };

  const warningLevel = getWarningLevel();
  const warningColors = {
    critical: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  };

  return (
    <Card className={`p-4 ${warningColors[warningLevel]}`}>
      <Alert className="border-0 bg-transparent p-0">
        <AlertTriangle className="h-5 w-5" />
        <AlertDescription className="ml-2">
          <div className="space-y-3">
            <div>
              <h4 className="font-semibold text-base mb-1">
        {isCriticalQuota ? '配额严重不足' : upgradeRequired ? '需要升级' : '配额不足'}
              </h4>
              <p className="text-sm opacity-90">
                当前文件需要 {creditsNeeded} 积分，但您的剩余配额不足
              </p>
            </div>

            {/* 配额使用情况 */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>配额使用情况</span>
                <span>{(remainingCharacters / 1000).toFixed(1)}K / {(totalCharacters / 1000).toFixed(1)}K 字符</span>
              </div>
              <Progress 
                value={usagePercentage} 
                className={`h-2 ${warningLevel === 'critical' ? 'bg-red-200' : 'bg-gray-200'}`}
              />
              <p className="text-xs opacity-75">
                已使用 {usagePercentage.toFixed(1)}% 
                {warningLevel === 'critical' && ' - 配额已耗尽'}
              </p>
        </div>
        
            {/* 操作按钮 */}
            <div className="flex space-x-2 pt-2">
              <Button
                size="sm"
                onClick={onUpgrade}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <ArrowUpCircle className="w-4 h-4 mr-1" />
                升级套餐
            </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onViewQuota}
                className="border-current text-current hover:bg-current/10"
              >
                <Eye className="w-4 h-4 mr-1" />
                查看配额
            </Button>
            </div>

            {/* 升级建议 */}
            {upgradeRequired && (
              <div className="text-xs opacity-75 pt-2 border-t border-current/20">
                💡 升级到 Plus 套餐可获得 5M 字符/月的配额，满足更大文件的处理需求
              </div>
            )}
        </div>
      </AlertDescription>
    </Alert>
    </Card>
  );
};

export default QuotaWarning;
