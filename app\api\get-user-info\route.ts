import { respData, respErr, respJson } from "@/lib/resp";

import { findUserByUuid } from "@/models/user";
import { getUserUuid } from "@/services/user";
import { QuotaService } from "@/services/quotaService";
import { getUserCredits } from "@/services/credit";

export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "no auth");
    }

    const user = await findUserByUuid(user_uuid);
    if (!user) {
      return respErr("user not exist");
    }

    // 获取用户积分信息（保持向后兼容）
    const userCredits = await getUserCredits(user_uuid);

    // 获取配额信息（新增）
    const quotaInfo = await QuotaService.getUserQuotaInfo(user_uuid);

    // 返回扩展的用户信息，保持向后兼容
    const userWithQuota = {
      ...user,
      credits: userCredits,  // 保持现有积分字段
      quota: quotaInfo,      // 新增配额信息
    };

    return respData(userWithQuota);
  } catch (e) {
    console.log("get user info failed: ", e);
    return respErr("get user info failed");
  }
}
