// ... existing code ...

{quiz.questions.map((question, index) => {
  // ... existing code above ...

  return (
    <Card key={question.id} className="overflow-hidden transition-all ...">
      <CardHeader
        className="cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => toggleQuestion(question.id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">

            <span className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold ${
              mode === 'management' && isSelected
                ? 'bg-blue-100 text-blue-800'
                : mode === 'taking' && hasAnswered
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
            }`}>
              {index + 1}
            </span>

            // ... 其余代码保持不变 ...

          </div>
          // ... existing code below ...
        </div>
      </CardHeader>

      // ... existing code ...
    </Card>
  );
})}

// ... existing code ...