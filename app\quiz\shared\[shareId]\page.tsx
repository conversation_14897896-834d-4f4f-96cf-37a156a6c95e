'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Quiz } from '@/types/quiz';
import QuizResultsEnhanced from '@/components/quiz/QuizResultsEnhanced';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Share2, Clock, AlertCircle } from "lucide-react";
import { toast } from "sonner";

export default function SharedQuizPage() {
  const params = useParams();
  const shareId = params.shareId as string;
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shareInfo, setShareInfo] = useState<any>(null);

  useEffect(() => {
    const fetchSharedQuiz = async () => {
      try {
        const response = await fetch(`/api/quiz/share?id=${shareId}`);
        const result = await response.json();

        if (result.success) {
          setQuiz(result.quiz);
          setShareInfo({
            sharedAt: result.sharedAt,
            expiresAt: result.expiresAt
          });
        } else {
          setError(result.error || 'Failed to load shared quiz');
        }
      } catch (err) {
        setError('Network error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (shareId) {
      fetchSharedQuiz();
    }
  }, [shareId]);

  const handleShare = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Share link copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy link');
    }
  };

  const handleExport = (format: string, selectedQuestions?: string[]) => {
    // 重用现有的导出逻辑
    console.log('Export:', format, selectedQuestions);
    toast.info('Export feature available in the main app');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading shared quiz...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardHeader className="text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <CardTitle className="text-red-600">Unable to Load Quiz</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">{error}</p>
            <Button 
              onClick={() => window.location.href = '/'}
              variant="outline"
            >
              Go to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!quiz) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 分享页面头部 */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Shared Quiz</h1>
              <p className="text-gray-600 mt-1">
                Shared on {shareInfo && formatDate(shareInfo.sharedAt)}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="flex items-center space-x-1">
                <Clock className="w-3 h-3" />
                <span className="text-xs">
                  Expires {shareInfo && formatDate(shareInfo.expiresAt)}
                </span>
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
              >
                <Share2 className="w-4 h-4 mr-2" />
                Copy Link
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 测验内容 */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <QuizResultsEnhanced
          quiz={quiz}
          mode="management"
          onExport={handleExport}
          onShare={handleShare}
        />
        
        {/* 底部信息 */}
        <div className="mt-8 text-center">
          <Card>
            <CardContent className="pt-6">
              <p className="text-sm text-gray-600 mb-4">
                Want to create your own quizzes? 
              </p>
              <Button 
                onClick={() => window.location.href = '/'}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Try PDFtoQuiz
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
