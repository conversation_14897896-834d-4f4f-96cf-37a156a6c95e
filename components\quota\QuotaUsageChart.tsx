'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface QuotaUsageChartProps {
  userUuid: string;
}

export default function QuotaUsageChart({ userUuid }: QuotaUsageChartProps) {
  // TODO: 实际获取月度使用数据
  const mockData = {
    currentMonth: {
      charactersUsed: 1200000, // 1.2M
      charactersTotal: 5000000, // 5M
      pagesUsed: 45,
      pagesTotal: 200,
      daysLeft: 15
    }
  };

  const charUsagePercent = (mockData.currentMonth.charactersUsed / mockData.currentMonth.charactersTotal) * 100;
  const pageUsagePercent = (mockData.currentMonth.pagesUsed / mockData.currentMonth.pagesTotal) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          本月使用统计
          <Badge variant="outline">
            {mockData.currentMonth.daysLeft} 天后重置
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 字符使用量 */}
        <div>
          <div className="flex justify-between text-sm mb-2">
            <span>字符配额</span>
            <span>
              {(mockData.currentMonth.charactersUsed / 1000).toFixed(1)}K / 
              {(mockData.currentMonth.charactersTotal / 1000).toFixed(1)}K
            </span>
          </div>
          <Progress value={charUsagePercent} className="h-2" />
        </div>

        {/* 页数使用量 */}
        <div>
          <div className="flex justify-between text-sm mb-2">
            <span>页数配额</span>
            <span>
              {mockData.currentMonth.pagesUsed} / {mockData.currentMonth.pagesTotal} 页
            </span>
          </div>
          <Progress value={pageUsagePercent} className="h-2" />
        </div>

        {/* 使用建议 */}
        {charUsagePercent > 80 && (
          <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
            ⚠️ 配额即将用完，建议升级计划或等待下月重置
          </div>
        )}
      </CardContent>
    </Card>
  );
} 